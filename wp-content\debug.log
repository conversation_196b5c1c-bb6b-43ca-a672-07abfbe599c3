[13-Aug-2025 15:16:53 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:16:53 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:16:53 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:16:55 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:16:55 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Aggiungendo pulsanti alla colonna azioni per ordine: 29037
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-refunded
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 1
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato NO MATCH per status wc-refunded
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 0
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-refunded, Pulsanti trovati: 0
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:16:56 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:16:56 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => dashboard
    [has_focus] => false
)

[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:16:57 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:16:57 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:16:58 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:16:58 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:17:03 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:03 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:03 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:03 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:17:03 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:17:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:03 UTC] WAA: POST data: Array
(
    [_wpnonce] => 2b374eb690
    [_wp_http_referer] => /wp-admin/post.php?post=29037&action=edit
    [user_ID] => 1
    [action] => editpost
    [originalaction] => editpost
    [post_author] => 1
    [post_type] => shop_order
    [original_post_status] => wc-refunded
    [referredby] => http://test.test/wp-admin/edit.php?post_type=shop_order
    [_wp_original_http_referer] => http://test.test/wp-admin/edit.php?post_type=shop_order
    [post_ID] => 29037
    [meta-box-order-nonce] => e33866cbd9
    [closedpostboxesnonce] => 935da108e9
    [original_post_title] => Order &ndash; Agosto 13, 2025 @ 04:00 PM
    [post_title] => Ordine
    [samplepermalinknonce] => e7300fba31
    [wc_order_action] => 
    [save] => Aggiorna
    [order_note] => 
    [order_note_type] => 
    [woocommerce_meta_nonce] => 956ffdfb92
    [post_status] => refunded
    [order_date] => 2025-08-13
    [order_date_hour] => 16
    [order_date_minute] => 00
    [order_date_second] => 56
    [order_status] => wc-processing
    [customer_user] => 1
    [_billing_first_name] => Giovanni
    [_billing_last_name] => Castaldo
    [_billing_company] => 
    [_billing_address_1] => via Giotto 13
    [_billing_address_2] => 
    [_billing_city] => Caserta
    [_billing_postcode] => 81100
    [_billing_country] => IT
    [_billing_state] => CE
    [_billing_email] => <EMAIL>
    [_billing_phone] => 
    [_payment_method] => bacs
    [_transaction_id] => 
    [_shipping_first_name] => Giovanni
    [_shipping_last_name] => Castaldo
    [_shipping_company] => 
    [_shipping_address_1] => via Giotto 13
    [_shipping_address_2] => 
    [_shipping_city] => Caserta
    [_shipping_postcode] => 81100
    [_shipping_country] => IT
    [_shipping_state] => CE
    [_shipping_phone] => 
    [customer_note] => 
    [order_item_id] => Array
        (
            [0] => 8
        )

    [order_item_tax_class] => Array
        (
            [8] => 
        )

    [order_item_qty] => Array
        (
            [8] => 2
        )

    [refund_order_item_qty] => Array
        (
            [8] => 
        )

    [line_subtotal] => Array
        (
            [8] => 858
        )

    [line_total] => Array
        (
            [8] => 858
        )

    [refund_line_total] => Array
        (
            [8] => 
        )

    [order_refund_id] => Array
        (
            [0] => 29038
        )

    [restock_refunded_items] => on
    [refund_amount] => 
    [refund_reason] => 
    [refunded_amount] => 858
    [meta] => Array
        (
            [3949] => Array
                (
                    [key] => is_vat_exempt
                    [value] => no
                )

        )

    [_ajax_nonce] => 7a1c696d40
    [metakeyselect] => #NONE#
    [metakeyinput] => 
    [metavalue] => 
    [_ajax_nonce-add-meta] => cb5e5ef417
)

[13-Aug-2025 15:17:04 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:04 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:04 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:05 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:17:05 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:06 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:06 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 2b374eb690
    [post_ID] => 29037
    [active_post_lock] => 1755098218:1
)

[13-Aug-2025 15:17:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:08 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:17:08 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Aggiungendo pulsanti alla colonna azioni per ordine: 29037
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 1
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 1
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 1
[13-Aug-2025 15:17:09 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:09 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:09 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:09 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:09 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:19 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:19 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:17:29 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:29 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:29 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:29 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:29 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:17:40 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:40 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:40 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:41 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:41 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:17:50 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:50 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:50 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:52 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:52 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:18:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:18:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:18:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:18:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:18:07 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:18:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:18:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:18:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:18:20 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:18:20 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:18:56 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:18:56 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:18:56 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:18:58 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:18:58 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => dashboard
    [has_focus] => false
)

[13-Aug-2025 15:19:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:19:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:19:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:19:53 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:19:53 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:20:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:20:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:20:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:20:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:20:07 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:20:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:20:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:20:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:20:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:20:22 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:21:52 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:21:52 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:21:52 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:21:54 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:21:54 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:22:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:22:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:22:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:22:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:22:07 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:23:53 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:23:53 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:23:53 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:23:55 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:23:55 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:24:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:08 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:24:08 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:24:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:34 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:24:34 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:35 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:24:35 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 1
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 1
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 1
[13-Aug-2025 15:24:37 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:37 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:37 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:38 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:24:38 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:24:42 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:42 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:42 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:43 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:24:43 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:24:46 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:46 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:46 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:47 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:24:47 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:24:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:01 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:25:01 UTC] WAA: POST data: Array
(
    [wosb_nonce] => 264146886c
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-order-status-buttons&action=edit&button_id=2
    [action] => wosb_save_button
    [button_id] => 2
    [button_name] => Rimborsato
    [button_description] => rimborsa
    [order_statuses] => Array
        (
            [0] => wc-pending
            [1] => wc-processing
            [2] => wc-on-hold
        )

    [target_status] => wc-refunded
    [dashicon_code] => \\f476
    [email_content] => test
)

[13-Aug-2025 15:25:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:02 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:02 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:25:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:07 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:07 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 1
[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 1
[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 1
[13-Aug-2025 15:25:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:11 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:25:11 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:13 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:13 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 1
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 1
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 1
[13-Aug-2025 15:25:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:17 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:25:17 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:25:17 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:17 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:25:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:21 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:21 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:25:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:34 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:25:34 UTC] WAA: POST data: Array
(
    [wosb_nonce] => 264146886c
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-order-status-buttons&action=edit&button_id=2
    [action] => wosb_save_button
    [button_id] => 2
    [button_name] => Rimborsato
    [button_description] => rimborsa
    [order_statuses] => Array
        (
            [0] => wc-pending
            [1] => wc-processing
            [2] => wc-on-hold
        )

    [target_status] => wc-refunded
    [dashicon_code] => \\f528
    [email_content] => test
)

[13-Aug-2025 15:25:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:35 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:35 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:26:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:08 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:08 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:08 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:08 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:26:08 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:26:09 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:26:09 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:26:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:23 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:26:23 UTC] WAA: POST data: Array
(
    [wosb_nonce] => 264146886c
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-order-status-buttons&action=add
    [action] => wosb_save_button
    [button_name] => Altro
    [button_description] => alslsls
    [order_statuses] => Array
        (
            [0] => wc-pending
            [1] => wc-processing
            [2] => wc-on-hold
        )

    [target_status] => wc-failed
    [dashicon_code] => \\f528
    [email_content] => dqdqwdqwdqw
)

[13-Aug-2025 15:26:24 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:24 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:24 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:24 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:26:24 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:26:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:29 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:26:29 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-processing
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 2
[13-Aug-2025 15:26:31 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:31 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:31 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:32 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:26:32 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:26:41 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:41 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:41 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:42 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:26:42 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:28:09 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:28:09 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:28:09 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:28:11 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:28:11 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:28:42 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:28:42 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:28:42 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:28:43 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:28:43 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:29:00 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:00 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:00 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:02 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:02 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:02 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:02 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:06 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:06 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:16 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:16 UTC] WAA: POST data: Array
(
    [wosb_nonce] => 264146886c
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-order-status-buttons&action=edit&button_id=3
    [action] => wosb_save_button
    [button_id] => 3
    [button_name] => Altro
    [button_description] => alslsls
    [order_statuses] => Array
        (
            [0] => wc-pending
            [1] => wc-processing
            [2] => wc-on-hold
        )

    [target_status] => wc-failed
    [dashicon_code] => \\f528
    [email_content] => dqdqwdqwdqw
)

[13-Aug-2025 15:29:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:17 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:17 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:20 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:20 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-processing
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 2
[13-Aug-2025 15:29:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:24 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:24 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:31 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:31 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:31 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:31 UTC] [WooCommerce Pulsanti Status Ordine] Email inviata con successo per l'ordine #29037
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:33 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:33 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:33 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:33 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-failed
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro NO MATCH per status wc-failed
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato NO MATCH per status wc-failed
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 0
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-failed, Pulsanti trovati: 0
[13-Aug-2025 15:29:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:36 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:36 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:45 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:45 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:45 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:45 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:45 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:47 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:47 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:47 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:47 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:47 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:48 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:48 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:48 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:49 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:49 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:51 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:51 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 2b374eb690
    [post_ID] => 29037
    [active_post_lock] => 1755098225:1
)

[13-Aug-2025 15:29:54 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:54 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:54 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:54 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:54 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:54 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:54 UTC] WAA: POST data: Array
(
    [_wpnonce] => 2b374eb690
    [_wp_http_referer] => /wp-admin/post.php?post=29037&action=edit
    [user_ID] => 1
    [action] => editpost
    [originalaction] => editpost
    [post_author] => 1
    [post_type] => shop_order
    [original_post_status] => wc-failed
    [referredby] => 
    [_wp_original_http_referer] => 
    [post_ID] => 29037
    [meta-box-order-nonce] => e33866cbd9
    [closedpostboxesnonce] => 935da108e9
    [original_post_title] => Order &ndash; Agosto 13, 2025 @ 04:00 PM
    [post_title] => Ordine
    [samplepermalinknonce] => e7300fba31
    [wc_order_action] => 
    [save] => Aggiorna
    [order_note] => 
    [order_note_type] => 
    [woocommerce_meta_nonce] => 956ffdfb92
    [post_status] => failed
    [order_date] => 2025-08-13
    [order_date_hour] => 16
    [order_date_minute] => 00
    [order_date_second] => 56
    [order_status] => wc-on-hold
    [customer_user] => 1
    [_billing_first_name] => Giovanni
    [_billing_last_name] => Castaldo
    [_billing_company] => 
    [_billing_address_1] => via Giotto 13
    [_billing_address_2] => 
    [_billing_city] => Caserta
    [_billing_postcode] => 81100
    [_billing_country] => IT
    [_billing_state] => CE
    [_billing_email] => <EMAIL>
    [_billing_phone] => 
    [_payment_method] => bacs
    [_transaction_id] => 
    [_shipping_first_name] => Giovanni
    [_shipping_last_name] => Castaldo
    [_shipping_company] => 
    [_shipping_address_1] => via Giotto 13
    [_shipping_address_2] => 
    [_shipping_city] => Caserta
    [_shipping_postcode] => 81100
    [_shipping_country] => IT
    [_shipping_state] => CE
    [_shipping_phone] => 
    [customer_note] => 
    [order_item_id] => Array
        (
            [0] => 8
        )

    [order_item_tax_class] => Array
        (
            [8] => 
        )

    [order_item_qty] => Array
        (
            [8] => 2
        )

    [refund_order_item_qty] => Array
        (
            [8] => 
        )

    [line_subtotal] => Array
        (
            [8] => 858
        )

    [line_total] => Array
        (
            [8] => 858
        )

    [refund_line_total] => Array
        (
            [8] => 
        )

    [order_refund_id] => Array
        (
            [0] => 29038
        )

    [restock_refunded_items] => on
    [refund_amount] => 
    [refund_reason] => 
    [refunded_amount] => 858
    [meta] => Array
        (
            [3949] => Array
                (
                    [key] => is_vat_exempt
                    [value] => no
                )

        )

    [_ajax_nonce] => 7a1c696d40
    [metakeyselect] => #NONE#
    [metakeyinput] => 
    [metavalue] => 
    [_ajax_nonce-add-meta] => cb5e5ef417
)

[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:56 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:56 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:29:56 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:56 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:58 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:58 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 2b374eb690
    [post_ID] => 29037
    [active_post_lock] => 1755098989:1
)

[13-Aug-2025 15:29:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:00 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:00 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:00 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:01 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:01 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-on-hold
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-on-hold
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-on-hold
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-on-hold, Pulsanti trovati: 2
[13-Aug-2025 15:30:02 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:02 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:02 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:30:03 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:30:05 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:05 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:05 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:05 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:05 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:14 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:14 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:15 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:15 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:15 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:15 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:15 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:15 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:20 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:20 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:20 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:21 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:21 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:30:21 UTC] WAA: POST data: Array
(
)

[13-Aug-2025 15:30:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:23 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:23 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:23 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-on-hold
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-on-hold
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-on-hold
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-on-hold, Pulsanti trovati: 2
[13-Aug-2025 15:30:24 UTC] WEAB: Added 5 buttons for order 29037
[13-Aug-2025 15:30:25 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:25 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:25 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:25 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:25 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:30:25 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:30:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:35 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:35 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:30:35 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:30:41 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:41 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:41 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:42 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:42 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:42 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:59 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:59 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:30:59 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:31:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:31:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:31:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:31:18 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:31:18 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:31:18 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:31:43 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:31:43 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:31:43 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:31:44 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:31:44 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:31:44 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:32:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:32:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:32:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:33:00 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:33:00 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:33:00 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:33:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:33:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:33:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:33:18 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:33:18 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:33:18 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:33:43 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:33:43 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:33:43 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:33:44 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:33:44 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:33:44 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:34:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:34:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:34:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:34:25 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:34:25 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:34:25 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:34:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:34:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:34:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:34:26 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:34:26 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:34:26 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:34:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:34:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:34:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:35:01 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:35:01 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:35:01 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:35:18 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:35:18 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:35:18 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:35:19 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:35:19 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:35:19 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:35:27 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:35:27 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:35:27 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:35:28 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:35:28 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:35:28 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:37:00 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:37:00 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:37:00 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:37:02 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:37:02 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:37:02 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:37:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:37:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:37:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:37:20 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:37:20 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:37:20 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:37:27 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:37:27 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:37:27 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:37:28 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:37:28 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:37:28 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:39:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:39:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:39:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:39:03 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:39:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:39:03 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:39:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:39:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:39:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:39:21 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:39:21 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:39:21 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:39:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:39:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:39:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:39:29 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:39:29 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:39:29 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:41:02 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:41:02 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:41:02 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:41:04 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:41:04 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:41:04 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:41:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:41:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:41:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:41:22 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:41:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:41:22 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:41:29 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:41:29 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:41:29 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:41:30 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:41:30 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:41:30 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:43:03 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:43:03 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:43:03 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:43:05 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:43:05 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:43:05 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:43:22 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:43:22 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:43:22 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:43:23 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:43:23 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:43:23 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:43:30 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:43:30 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:43:30 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:43:31 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:43:31 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:43:31 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:44:56 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:44:56 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:44:56 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:44:57 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:44:57 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:44:57 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => true
)

[13-Aug-2025 15:44:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:44:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:44:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:44:59 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:44:59 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:44:59 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:03 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:03 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:03 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:03 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:45:03 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:03 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:05 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:05 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:05 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:45:05 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:45:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:08 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:08 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:09 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:09 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:13 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:13 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:15 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:15 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:18 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:18 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:18 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:19 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:19 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:24 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:24 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:24 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:24 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:45:24 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:45:24 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:24 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:53 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:45:53 UTC] WAA: POST data: Array
(
    [wosb_nonce] => 264146886c
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-order-status-buttons&action=edit&button_id=3
    [action] => wosb_save_button
    [button_id] => 3
    [button_name] => Altro
    [button_description] => alslsls
    [order_statuses] => Array
        (
            [0] => wc-pending
            [1] => wc-processing
            [2] => wc-on-hold
        )

    [target_status] => wc-failed
    [dashicon_code] => \\\\f528
    [email_subject] => Ordine {order_number} Fallito
    [email_content] => {order_number}dqdqwdqwdqw
)

[13-Aug-2025 15:45:54 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:54 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:54 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:54 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:54 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:46:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:46:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:46:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:46:57 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:46:57 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:47:05 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:47:05 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:47:05 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:47:06 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:47:06 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:47:24 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:47:24 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:47:24 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:47:25 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:47:25 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:48:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:48:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:48:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:48:57 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:48:57 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:49:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:49:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:49:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:49:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:49:07 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:49:25 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:49:25 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:49:25 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:49:26 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:49:26 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:50:56 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:50:56 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:50:56 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:50:58 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:50:58 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:51:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:51:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:51:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:51:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:51:07 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:51:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:51:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:51:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:51:27 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:51:27 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:52:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:52:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:52:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:52:59 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:52:59 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:53:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:53:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:53:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:53:08 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:53:08 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:53:27 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:53:27 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:53:27 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:53:28 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:53:28 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:54:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:54:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:54:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:55:00 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:55:00 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:55:08 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:55:08 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:55:08 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:55:09 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:55:09 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:55:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:55:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:55:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:55:29 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:55:29 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:56:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:56:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:56:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:57:01 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:57:01 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:57:09 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:57:09 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:57:09 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:57:10 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:57:10 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:57:29 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:57:29 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:57:29 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:57:30 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:57:30 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:59:00 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:59:00 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:59:00 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:59:02 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:59:02 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:59:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:59:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:59:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:59:11 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:59:11 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:59:30 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:59:30 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:59:30 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:59:31 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:59:31 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:01:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:01:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:01:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:01:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:01:03 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:01:11 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:01:11 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:01:11 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:01:12 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:01:12 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:01:31 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:01:31 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:01:31 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:01:32 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:01:32 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:03:02 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:03:02 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:03:02 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:03:04 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:03:04 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:03:12 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:03:12 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:03:12 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:03:13 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:03:13 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:03:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:03:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:03:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:03:33 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:03:33 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:05:03 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:05:03 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:05:03 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:05:05 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:05:05 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:05:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:05:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:05:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:05:14 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:05:14 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:05:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:05:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:05:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:05:34 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:05:34 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:07:04 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:07:04 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:07:04 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:07:06 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:07:06 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:07:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:07:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:07:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:07:15 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:07:15 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:07:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:07:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:07:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:07:35 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:07:35 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:09:05 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:09:05 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:09:05 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:09:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:09:07 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:09:15 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:09:15 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:09:15 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:09:16 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:09:16 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:09:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:09:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:09:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:09:36 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:09:36 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:11:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:11:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:11:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:11:08 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:11:08 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:11:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:11:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:11:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:11:17 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:11:17 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:11:36 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:11:36 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:11:36 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:11:37 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:11:37 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:13:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:13:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:13:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:13:09 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:13:09 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:13:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:13:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:13:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:13:18 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:13:18 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:13:37 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:13:37 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:13:37 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:13:38 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:13:38 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:15:08 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:15:08 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:15:08 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:15:10 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:15:10 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:15:18 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:15:18 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:15:18 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:15:19 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:15:19 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:15:38 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:15:38 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:15:38 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:15:39 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:15:39 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:17:09 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:17:09 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:17:09 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:17:11 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:17:11 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:17:20 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:17:20 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:17:39 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:17:39 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:17:39 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:17:40 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:17:40 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:19:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:19:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:19:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:19:12 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:19:12 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:19:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:19:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:19:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:19:21 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:19:21 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:19:40 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:19:40 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:19:40 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:19:41 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:19:41 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:21:11 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:21:11 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:21:11 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:21:13 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:21:13 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:21:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:21:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:21:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:21:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:21:22 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:21:41 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:21:41 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:21:41 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:21:42 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:21:42 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:23:12 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:23:12 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:23:12 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:23:14 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:23:14 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:23:22 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:23:22 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:23:22 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:23:23 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:23:23 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:23:42 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:23:42 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:23:42 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:23:43 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:23:43 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:25:15 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:25:15 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:25:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:25:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:25:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:25:24 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:25:24 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:25:43 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:25:43 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:25:43 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:25:44 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:25:44 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:27:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:27:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:27:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:27:16 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:27:16 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:27:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:27:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:27:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:27:24 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:27:24 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:27:44 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:27:44 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:27:44 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:27:45 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:27:45 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:29:15 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:29:15 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:29:15 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:29:17 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:29:17 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:29:24 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:29:24 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:29:24 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:29:25 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:29:25 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:29:45 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:29:45 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:29:45 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:29:46 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:29:46 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:31:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:31:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:31:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:31:18 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:31:18 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:31:25 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:31:25 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:31:25 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:31:26 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:31:26 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:31:46 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:31:46 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:31:46 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:31:47 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:31:47 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:33:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:33:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:33:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:33:19 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:33:19 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:33:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:33:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:33:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:33:27 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:33:27 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:33:47 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:33:47 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:33:47 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:33:48 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:33:48 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:35:18 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:35:18 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:35:18 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:35:20 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:35:20 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:35:27 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:35:27 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:35:27 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:35:28 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:35:28 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:35:48 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:35:48 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:35:48 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:35:49 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:35:49 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:37:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:37:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:37:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:37:21 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:37:21 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:37:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:37:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:37:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:37:29 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:37:29 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:37:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:37:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:37:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:37:51 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:37:51 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:39:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:39:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:39:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:39:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:39:22 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:39:29 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:39:29 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:39:29 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:39:30 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:39:30 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:39:50 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:39:50 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:39:50 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:39:52 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:39:52 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:41:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:41:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:41:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:41:23 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:41:23 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:41:30 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:41:30 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:41:30 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:41:31 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:41:31 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:41:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:41:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:41:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:41:53 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:41:53 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:43:22 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:43:22 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:43:22 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:43:24 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:43:24 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:43:31 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:43:31 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:43:31 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:43:32 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:43:32 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:43:52 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:43:52 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:43:52 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:43:54 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:43:54 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:44:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:44:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:44:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:44:14 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:44:14 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:44:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:44:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:44:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:44:20 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:44:20 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:44:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:44:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:44:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:44:21 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:44:21 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:44:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:44:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:44:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:44:24 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:44:24 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:44:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:44:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:44:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:44:26 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:44:26 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:44:26 UTC] WordPress database error Table 'test.wp_woo_hide_shipping_rules' doesn't exist for query SELECT * FROM wp_woo_hide_shipping_rules ORDER BY created_at DESC made by do_action('woocommerce_page_woo-hide-shipping'), WP_Hook->do_action, WP_Hook->apply_filters, WooHideShipping_Admin->admin_page, WooHideShipping_Admin->render_rules_list, WooHideShipping_Database::get_rules
[13-Aug-2025 16:44:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:44:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:44:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:44:29 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:44:29 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:44:48 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:44:48 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:44:48 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:44:48 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:44:48 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:44:48 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:44:48 UTC] WAA: POST data: Array
(
    [woo_hide_shipping_nonce] => 99c12a9957
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-hide-shipping&action=add
    [action] => add_rule
    [rule_name] => Test
    [payment_methods] => Array
        (
            [0] => bacs
        )

    [shipping_methods] => Array
        (
            [0] => free_shipping
        )

    [status] => active
)

[13-Aug-2025 16:44:48 UTC] WordPress database error Table 'test.wp_woo_hide_shipping_rules' doesn't exist for query SHOW FULL COLUMNS FROM `wp_woo_hide_shipping_rules` made by do_action('admin_init'), WP_Hook->do_action, WP_Hook->apply_filters, WooHideShipping_Admin->handle_form_submission, WooHideShipping_Admin->handle_add_rule, WooHideShipping_Database::insert_rule
[13-Aug-2025 16:45:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:45:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:45:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:45:12 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:45:12 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:45:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:45:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:45:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:45:18 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:45:18 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:45:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:45:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:45:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:45:24 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:45:24 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:45:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:45:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:45:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:45:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:45:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:45:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:45:33 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:45:33 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:45:33 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:45:33 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:45:33 UTC] WordPress database error Table 'test.wp_woo_hide_shipping_rules' doesn't exist for query SELECT * FROM wp_woo_hide_shipping_rules ORDER BY created_at DESC made by do_action('woocommerce_page_woo-hide-shipping'), WP_Hook->do_action, WP_Hook->apply_filters, WooHideShipping_Admin->admin_page, WooHideShipping_Admin->render_rules_list, WooHideShipping_Database::get_rules
[13-Aug-2025 16:45:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:45:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:45:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:45:36 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:45:36 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:45:43 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:45:43 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:45:43 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:45:43 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:45:43 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:45:43 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:45:43 UTC] WAA: POST data: Array
(
    [woo_hide_shipping_nonce] => 99c12a9957
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-hide-shipping&action=add
    [action] => add_rule
    [rule_name] => test
    [payment_methods] => Array
        (
            [0] => cod
        )

    [shipping_methods] => Array
        (
            [0] => free_shipping
        )

    [status] => active
)

[13-Aug-2025 16:45:43 UTC] WordPress database error Table 'test.wp_woo_hide_shipping_rules' doesn't exist for query SHOW FULL COLUMNS FROM `wp_woo_hide_shipping_rules` made by do_action('admin_init'), WP_Hook->do_action, WP_Hook->apply_filters, WooHideShipping_Admin->handle_form_submission, WooHideShipping_Admin->handle_add_rule, WooHideShipping_Database::insert_rule
[13-Aug-2025 16:45:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:45:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:45:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:45:51 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:45:51 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:46:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:46:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:46:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:46:06 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:46:06 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:46:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:46:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:46:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:46:10 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:46:10 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:46:11 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:46:11 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:46:11 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:46:12 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:46:12 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:46:24 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:46:24 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:46:24 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:46:25 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:46:25 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:47:12 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:47:12 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:47:12 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:47:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:47:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:47:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:47:13 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:47:13 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:47:14 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:47:14 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => true
)

[13-Aug-2025 16:47:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:47:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:47:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:47:15 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:47:15 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:47:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:47:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:47:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:47:20 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:47:20 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:47:20 UTC] WordPress database error Table 'test.wp_woo_hide_shipping_rules' doesn't exist for query SELECT * FROM wp_woo_hide_shipping_rules ORDER BY created_at DESC made by do_action('woocommerce_page_woo-hide-shipping'), WP_Hook->do_action, WP_Hook->apply_filters, WooHideShipping_Admin->admin_page, WooHideShipping_Admin->render_rules_list, WooHideShipping_Database::get_rules
[13-Aug-2025 16:47:22 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:47:22 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:47:22 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:47:22 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:47:22 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:47:31 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:47:31 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:47:31 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:47:32 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:47:32 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:47:32 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:47:32 UTC] WAA: POST data: Array
(
    [woo_hide_shipping_nonce] => 99c12a9957
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-hide-shipping&action=add
    [action] => add_rule
    [rule_name] => testst
    [payment_methods] => Array
        (
            [0] => bacs
        )

    [shipping_methods] => Array
        (
            [0] => flat_rate
        )

    [status] => active
)

[13-Aug-2025 16:47:32 UTC] WordPress database error Table 'test.wp_woo_hide_shipping_rules' doesn't exist for query SHOW FULL COLUMNS FROM `wp_woo_hide_shipping_rules` made by do_action('admin_init'), WP_Hook->do_action, WP_Hook->apply_filters, WooHideShipping_Admin->handle_form_submission, WooHideShipping_Admin->handle_add_rule, WooHideShipping_Database::insert_rule
[13-Aug-2025 16:47:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:47:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:47:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:47:34 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:47:34 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:47:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:47:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:47:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:47:51 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:47:51 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:48:25 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:48:25 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:48:25 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:48:26 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:48:26 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:48:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:48:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:48:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:48:27 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:48:27 UTC] WAA: POST data: Array
(
)

[13-Aug-2025 16:48:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:48:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:48:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:48:34 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:48:34 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:49:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:49:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:49:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:49:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:49:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:49:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:49:35 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:49:35 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:49:35 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:49:35 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:49:50 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:49:50 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:49:50 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:49:52 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:49:52 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:50:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:50:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:50:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:50:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:50:22 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 16:50:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:50:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:50:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:50:27 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:50:27 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:50:31 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:50:31 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:50:31 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:50:32 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:50:32 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 16:50:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:50:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:50:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:50:35 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:50:35 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:50:38 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:50:38 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:50:38 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:50:39 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:50:39 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - Creating table: wp_woo_hide_shipping_rules
[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - SQL: CREATE TABLE wp_woo_hide_shipping_rules (
            id int(11) NOT NULL AUTO_INCREMENT,
            rule_name varchar(255) NOT NULL,
            payment_methods longtext NOT NULL,
            shipping_methods longtext NOT NULL,
            status enum('active','inactive') DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - dbDelta result: Array
(
    [wp_woo_hide_shipping_rules] => Created table wp_woo_hide_shipping_rules
)

[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - Table exists after creation: YES
[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - Table name: wp_woo_hide_shipping_rules
[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - Original data: Array
(
    [rule_name] => Test Rule
    [payment_methods] => Array
        (
            [0] => bacs
        )

    [shipping_methods] => Array
        (
            [0] => flat_rate
        )

    [status] => active
)

[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - Serialized data: Array
(
    [rule_name] => Test Rule
    [payment_methods] => a:1:{i:0;s:4:"bacs";}
    [shipping_methods] => a:1:{i:0;s:9:"flat_rate";}
    [status] => active
)

[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - Insert data: Array
(
    [rule_name] => Test Rule
    [payment_methods] => a:1:{i:0;s:4:"bacs";}
    [shipping_methods] => a:1:{i:0;s:9:"flat_rate";}
    [status] => active
)

[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - wpdb->insert result: 1
[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - Last query: INSERT INTO `wp_woo_hide_shipping_rules` (`rule_name`, `payment_methods`, `shipping_methods`, `status`) VALUES ('Test Rule', 'a:1:{i:0;s:4:\"bacs\";}', 'a:1:{i:0;s:9:\"flat_rate\";}', 'active')
[13-Aug-2025 16:50:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:50:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:50:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:50:49 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:50:49 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:50:54 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:50:54 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:50:54 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:50:56 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:50:56 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:50:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:50:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:50:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:50:59 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:50:59 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:51:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:06 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:51:06 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:51:06 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:51:06 UTC] WAA: POST data: Array
(
    [woo_hide_shipping_nonce] => 99c12a9957
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-hide-shipping&action=add
    [action] => add_rule
    [rule_name] => papapap
    [payment_methods] => Array
        (
            [0] => cheque
        )

    [shipping_methods] => Array
        (
            [0] => flat_rate
        )

    [status] => active
)

[13-Aug-2025 16:51:06 UTC] WooHideShipping Debug - Rule data: Array
(
    [rule_name] => papapap
    [payment_methods] => Array
        (
            [0] => cheque
        )

    [shipping_methods] => Array
        (
            [0] => flat_rate
        )

    [status] => active
)

[13-Aug-2025 16:51:06 UTC] WooHideShipping Debug - Table name: wp_woo_hide_shipping_rules
[13-Aug-2025 16:51:06 UTC] WooHideShipping Debug - Original data: Array
(
    [rule_name] => papapap
    [payment_methods] => Array
        (
            [0] => cheque
        )

    [shipping_methods] => Array
        (
            [0] => flat_rate
        )

    [status] => active
)

[13-Aug-2025 16:51:06 UTC] WooHideShipping Debug - Serialized data: Array
(
    [rule_name] => papapap
    [payment_methods] => a:1:{i:0;s:6:"cheque";}
    [shipping_methods] => a:1:{i:0;s:9:"flat_rate";}
    [status] => active
)

[13-Aug-2025 16:51:06 UTC] WooHideShipping Debug - Insert data: Array
(
    [rule_name] => papapap
    [payment_methods] => a:1:{i:0;s:6:"cheque";}
    [shipping_methods] => a:1:{i:0;s:9:"flat_rate";}
    [status] => active
)

[13-Aug-2025 16:51:06 UTC] WooHideShipping Debug - wpdb->insert result: 1
[13-Aug-2025 16:51:06 UTC] WooHideShipping Debug - Last query: INSERT INTO `wp_woo_hide_shipping_rules` (`rule_name`, `payment_methods`, `shipping_methods`, `status`) VALUES ('papapap', 'a:1:{i:0;s:6:\"cheque\";}', 'a:1:{i:0;s:9:\"flat_rate\";}', 'active')
[13-Aug-2025 16:51:06 UTC] WooHideShipping Debug - Rule inserted successfully with ID: 2
[13-Aug-2025 16:51:09 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:09 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:09 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:10 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:51:10 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:51:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:15 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:51:15 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:51:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:22 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:51:22 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:51:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:51:22 UTC] WAA: POST data: Array
(
    [woo_hide_shipping_nonce] => 99c12a9957
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-hide-shipping&action=edit&rule_id=2
    [action] => edit_rule
    [rule_id] => 2
    [rule_name] => papapap2121212
    [payment_methods] => Array
        (
            [0] => bacs
        )

    [shipping_methods] => Array
        (
            [0] => flat_rate
        )

    [status] => active
)

[13-Aug-2025 16:51:24 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:24 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:24 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:25 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:51:25 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:51:30 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:30 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:30 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:31 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:51:31 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:51:31 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:51:31 UTC] WAA: POST data: Array
(
    [woo_hide_shipping_nonce] => 99c12a9957
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-hide-shipping
    [action] => delete_rule
    [rule_id] => 2
)

[13-Aug-2025 16:51:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:36 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:51:36 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:51:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:52 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:51:52 UTC] WAA: POST data: Array
(
    [action] => wpaj_add_accessories_to_cart
    [product_id] => 381
    [quantity] => 1
    [nonce] => 1842142e2e
)

[13-Aug-2025 16:51:52 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:51:52 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:51:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:56 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:51:56 UTC] WAA: POST data: Array
(
    [action] => wpaj_add_accessories_to_cart
    [product_id] => 381
    [quantity] => 1
    [nonce] => 1842142e2e
)

[13-Aug-2025 16:52:27 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:52:27 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:52:27 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:52:28 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:52:28 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:52:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:52:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:52:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:52:33 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:52:33 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:52:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:52:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:52:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:52:36 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:52:36 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:52:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:52:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:52:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:52:51 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:52:51 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => true
)

[13-Aug-2025 16:52:52 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:52:52 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:52:52 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:52:53 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:52:53 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:52:54 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:52:54 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:52:54 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:52:55 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:52:55 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 2b374eb690
    [post_ID] => 29037
    [active_post_lock] => 1755098996:1
)

[13-Aug-2025 16:52:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:52:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:52:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:52:56 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:52:56 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:52:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:52:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:52:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:52:59 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:52:59 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:53:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:53:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:53:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:53:15 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:53:15 UTC] WAA: POST data: Array
(
    [wc_shipping_zones_nonce] => d24c3e93e5
    [method_id] => free_shipping
    [zone_id] => 
)

[13-Aug-2025 16:53:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:53:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:53:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:53:18 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:53:18 UTC] WAA: POST data: Array
(
    [wc_shipping_zones_nonce] => d24c3e93e5
    [instance_id] => 1
    [data] => Array
        (
            [woocommerce_free_shipping_title] => Spedizione gratuita
            [woocommerce_free_shipping_requires] => 
            [woocommerce_free_shipping_min_amount] => 0
            [instance_id] => 1
        )

)

[13-Aug-2025 16:53:18 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:53:18 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:53:18 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:53:19 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:53:19 UTC] WAA: POST data: Array
(
    [wc_shipping_zones_nonce] => d24c3e93e5
    [changes] => Array
        (
            [zone_name] => italia
            [zone_locations] => Array
                (
                    [0] => country:IT
                )

        )

    [zone_id] => 1
)

[13-Aug-2025 16:53:22 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:53:22 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:53:22 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:53:23 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:53:23 UTC] WAA: POST data: Array
(
    [wc_shipping_zones_nonce] => d24c3e93e5
    [method_id] => flat_rate
    [zone_id] => 1
)

[13-Aug-2025 16:53:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:53:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:53:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:53:26 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:53:26 UTC] WAA: POST data: Array
(
    [wc_shipping_zones_nonce] => d24c3e93e5
    [instance_id] => 2
    [data] => Array
        (
            [woocommerce_flat_rate_title] => Tariffa unica
            [woocommerce_flat_rate_tax_status] => taxable
            [woocommerce_flat_rate_cost] => 3
            [instance_id] => 2
        )

)

[13-Aug-2025 16:53:52 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:53:52 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:53:52 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:53:54 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:53:54 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:53:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:53:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:53:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:53:55 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:53:55 UTC] WAA: POST data: Array
(
)

[13-Aug-2025 16:54:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:54:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:54:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:54:02 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:54:02 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 16:54:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:54:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:54:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:54:29 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:54:29 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:54:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:54:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:54:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:54:33 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:54:33 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:54:36 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:54:36 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:54:36 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:54:37 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:54:37 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:56:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:56:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:56:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:56:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:56:03 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 16:56:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:56:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:56:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:56:34 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:56:34 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:56:37 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:56:37 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:56:37 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:56:38 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:56:38 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:58:02 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:58:02 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:58:02 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:58:04 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:58:04 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 16:58:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:58:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:58:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:58:35 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:58:35 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:58:38 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:58:38 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:58:38 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:58:39 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:58:39 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:00:03 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:00:03 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:00:03 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:00:05 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:00:05 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:00:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:00:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:00:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:00:36 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:00:36 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:00:39 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:00:39 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:00:39 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:00:40 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:00:40 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:01:56 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:01:56 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:01:56 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:01:58 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:01:58 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => true
)

[13-Aug-2025 17:01:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:01:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:01:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:01:59 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 17:01:59 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 17:02:36 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:02:36 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:02:36 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:02:37 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:02:37 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:03:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:03:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:03:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:03:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:03:03 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:04:37 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:04:37 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:04:37 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:04:39 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:04:39 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:05:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:05:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:05:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:05:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:05:03 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:06:38 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:06:38 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:06:38 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:06:40 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:06:40 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:07:02 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:07:02 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:07:02 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:07:04 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:07:04 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:08:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:08:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:08:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:08:36 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:08:36 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => true
)

[13-Aug-2025 17:08:39 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:08:39 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:08:39 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:08:40 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:08:40 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:10:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:10:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:10:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:10:36 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:10:36 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:10:40 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:10:40 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:10:40 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:10:41 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:10:41 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:12:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:12:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:12:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:12:37 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:12:37 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:12:41 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:12:41 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:12:41 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:12:42 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:12:42 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:13:36 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:13:36 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:13:36 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:13:38 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:13:38 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:14:42 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:14:42 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:14:42 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:14:44 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:14:44 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:15:37 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:15:37 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:15:37 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:15:39 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:15:39 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:16:43 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:16:43 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:16:43 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:16:45 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:16:45 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:17:38 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:17:38 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:17:38 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:17:40 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:17:40 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:18:44 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:18:44 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:18:44 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:18:46 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:18:46 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:19:39 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:19:39 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:19:39 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:19:41 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:19:41 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:20:45 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:20:45 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:20:45 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:20:47 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:20:47 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:21:40 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:21:40 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:21:40 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:21:42 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:21:42 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:22:46 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:22:46 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:22:46 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:22:48 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:22:48 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:23:41 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:23:41 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:23:41 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:23:43 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:23:43 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:24:47 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:24:47 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:24:47 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:24:49 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:24:49 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:26:48 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:26:48 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:26:48 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:26:50 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:26:50 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:28:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:28:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:28:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:28:51 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:28:51 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:30:50 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:30:50 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:30:50 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:30:52 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:30:52 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:32:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:32:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:32:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:32:53 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:32:53 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:34:52 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:34:52 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:34:52 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:34:54 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:34:54 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:36:53 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:36:53 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:36:53 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:36:55 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:36:55 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:38:54 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:38:54 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:38:54 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:38:56 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:38:56 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:40:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:40:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:40:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:40:57 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:40:57 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:42:56 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:42:56 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:42:56 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:42:58 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:42:58 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:44:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:44:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:44:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:44:59 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:44:59 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:46:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:46:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:46:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:47:00 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:47:00 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:48:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:48:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:48:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:49:01 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:49:01 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:51:00 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:51:00 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:51:00 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:51:02 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:51:02 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:53:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:53:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:53:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:53:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:53:03 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:55:02 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:55:02 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:55:02 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:55:04 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:55:04 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:57:03 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:57:03 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:57:03 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:57:05 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:57:05 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:59:04 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:59:04 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:59:04 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:59:06 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:59:06 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:01:05 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:01:05 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:01:05 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:01:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:01:07 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:03:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:03:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:03:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:03:08 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:03:08 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:05:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:05:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:05:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:05:09 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:05:09 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:07:08 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:07:08 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:07:08 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:07:10 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:07:10 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:09:09 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:09:09 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:09:09 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:09:11 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:09:11 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:11:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:11:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:11:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:11:12 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:11:12 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:13:11 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:13:11 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:13:11 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:13:13 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:13:13 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:15:12 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:15:12 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:15:12 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:15:14 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:15:14 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:16:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:16:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:16:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:16:53 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:16:53 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => true
)

[13-Aug-2025 18:16:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:16:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:16:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:17:00 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:17:00 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:17:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:17:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:17:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:17:11 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:17:11 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => true
)

[13-Aug-2025 18:17:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:17:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:17:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:17:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:17:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:17:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:17:14 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:17:14 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:17:14 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:17:14 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:17:20 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:17:20 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:17:22 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:17:22 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:17:22 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:17:23 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:17:23 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:17:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:17:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:17:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:17:27 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:17:27 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:17:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:17:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:17:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:17:33 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:17:33 UTC] WAA: POST data: Array
(
    [wc_shipping_zones_nonce] => d24c3e93e5
    [method_id] => flat_rate
    [zone_id] => 1
)

[13-Aug-2025 18:17:41 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:17:41 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:17:41 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:17:42 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:17:42 UTC] WAA: POST data: Array
(
    [wc_shipping_zones_nonce] => d24c3e93e5
    [instance_id] => 3
    [data] => Array
        (
            [woocommerce_flat_rate_title] => Spedizione assicurata
            [woocommerce_flat_rate_tax_status] => taxable
            [woocommerce_flat_rate_cost] => 3
            [instance_id] => 3
        )

)

[13-Aug-2025 18:17:47 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:17:47 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:17:47 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:17:48 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:17:48 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:17:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:17:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:17:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:17:52 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:17:52 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:18:02 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:18:02 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:18:02 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:18:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:18:03 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:18:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:18:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:18:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:18:17 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:18:17 UTC] WAA: POST data: Array
(
    [wc_shipping_zones_nonce] => d24c3e93e5
    [method_id] => flat_rate
    [zone_id] => 
)

[13-Aug-2025 18:18:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:18:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:18:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:18:29 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:18:29 UTC] WAA: POST data: Array
(
    [wc_shipping_zones_nonce] => d24c3e93e5
    [instance_id] => 4
    [data] => Array
        (
            [woocommerce_flat_rate_title] => Spedizione assicurata campania
            [woocommerce_flat_rate_tax_status] => taxable
            [woocommerce_flat_rate_cost] => 4
            [instance_id] => 4
        )

)

[13-Aug-2025 18:18:29 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:18:29 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:18:29 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:18:30 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:18:30 UTC] WAA: POST data: Array
(
    [wc_shipping_zones_nonce] => d24c3e93e5
    [changes] => Array
        (
            [zone_name] => Campania
            [zone_locations] => Array
                (
                    [0] => state:IT:CE
                    [1] => state:IT:NA
                    [2] => state:IT:AV
                    [3] => state:IT:SA
                )

        )

    [zone_id] => 2
)

[13-Aug-2025 18:18:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:18:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:18:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:18:36 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:18:36 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:18:53 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:18:53 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:18:53 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:18:55 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:18:55 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 18:18:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:18:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:18:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:18:58 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:18:58 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:18:58 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:18:58 UTC] WAA: POST data: Array
(
    [woo_hide_shipping_nonce] => 99c12a9957
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-hide-shipping&action=edit&rule_id=1
    [action] => edit_rule
    [rule_id] => 1
    [rule_name] => Test Rule
    [payment_methods] => Array
        (
            [0] => bacs
        )

    [shipping_methods] => Array
        (
            [0] => flat_rate:3
        )

    [status] => active
)

[13-Aug-2025 18:19:02 UTC] WooHideShipping Debug - Chosen payment method: cheque
[13-Aug-2025 18:19:02 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 18:19:02 UTC] WooHideShipping Debug - Found rules: 0
[13-Aug-2025 18:19:02 UTC] WooHideShipping Debug - No rules found for payment method: cheque
[13-Aug-2025 18:19:02 UTC] WooHideShipping Debug - Chosen payment method: cheque
[13-Aug-2025 18:19:02 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 18:19:02 UTC] WooHideShipping Debug - Found rules: 0
[13-Aug-2025 18:19:02 UTC] WooHideShipping Debug - No rules found for payment method: cheque
[13-Aug-2025 18:19:02 UTC] WooHideShipping Debug - Chosen payment method: cheque
[13-Aug-2025 18:19:02 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 18:19:02 UTC] WooHideShipping Debug - Found rules: 0
[13-Aug-2025 18:19:02 UTC] WooHideShipping Debug - No rules found for payment method: cheque
[13-Aug-2025 18:19:10 UTC] WooHideShipping Debug - Chosen payment method: bacs
[13-Aug-2025 18:19:10 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 18:19:10 UTC] WooHideShipping Debug - Found rules: 1
[13-Aug-2025 18:19:10 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:19:10 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:19:10 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:19:10 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[13-Aug-2025 18:19:10 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:19:10 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[13-Aug-2025 18:19:10 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:19:10 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[13-Aug-2025 18:19:10 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[13-Aug-2025 18:19:10 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[13-Aug-2025 18:19:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:19:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:19:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:19:15 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:19:15 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:19:15 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:19:15 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:19:15 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:19:16 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:19:16 UTC] WAA: POST data: Array
(
)

[13-Aug-2025 18:19:50 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:19:50 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:19:50 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:19:52 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:19:52 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:19:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:19:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:19:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:19:56 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:19:56 UTC] WAA: POST data: Array
(
    [action] => woodmart_get_theme_settings_search_data
    [security] => 61e2eaa61a
    [is_preset] => no
)

[13-Aug-2025 18:19:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:19:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:19:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:20:00 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:20:00 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:20:08 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:20:08 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:20:08 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:20:08 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:20:08 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:20:08 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:20:08 UTC] WAA: POST data: Array
(
    [page_options] => xts-woodmart-options
    [action] => update
    [option_page] => xts-options-group
    [_wpnonce] => 6ed68d8457
    [_wp_http_referer] => /wp-admin/admin.php?page=xts_theme_settings&tab=general_shop_section
    [xts-woodmart-options] => Array
        (
            [checkout_show_product_image] => 1
            [checkout_product_quantity] => 1
            [checkout_remove_button] => 1
            [checkout_link_to_product] => 1
            [checkout_fields_enabled] => 1
        )

)

[13-Aug-2025 18:20:09 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:20:09 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:20:09 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:20:10 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:20:10 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:20:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:20:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:20:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:20:14 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:20:14 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:20:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:20:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:20:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Chosen payment method: bacs
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Found rules: 1
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Chosen payment method: bacs
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Found rules: 1
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Chosen payment method: bacs
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Found rules: 1
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[13-Aug-2025 18:20:17 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[13-Aug-2025 18:20:17 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:20:17 UTC] WAA: POST data: Array
(
    [action] => woodmart_get_theme_settings_search_data
    [security] => 61e2eaa61a
    [is_preset] => no
)

[13-Aug-2025 18:20:30 UTC] WooHideShipping Debug - Chosen payment method: bacs
[13-Aug-2025 18:20:30 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 18:20:30 UTC] WooHideShipping Debug - Found rules: 1
[13-Aug-2025 18:20:30 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:30 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:30 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:30 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[13-Aug-2025 18:20:30 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:30 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[13-Aug-2025 18:20:30 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:30 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[13-Aug-2025 18:20:30 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[13-Aug-2025 18:20:30 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Chosen payment method: bacs
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Found rules: 1
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Chosen payment method: bacs
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Found rules: 1
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Chosen payment method: bacs
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Found rules: 1
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[13-Aug-2025 18:20:31 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Chosen payment method: bacs
[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Found rules: 1
[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Chosen payment method: bacs
[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Found rules: 1
[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[13-Aug-2025 18:20:33 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[13-Aug-2025 18:20:45 UTC] WooHideShipping Debug - Chosen payment method: cheque
[13-Aug-2025 18:20:45 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 18:20:45 UTC] WooHideShipping Debug - Found rules: 0
[13-Aug-2025 18:20:45 UTC] WooHideShipping Debug - No rules found for payment method: cheque
[13-Aug-2025 18:20:54 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:20:54 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:20:54 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:20:56 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:20:56 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 18:21:15 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:21:15 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:21:15 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:21:16 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:21:16 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => toplevel_page_xts_theme_settings
    [has_focus] => true
)

[13-Aug-2025 18:21:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:21:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:21:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:21:19 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:21:19 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:21:40 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:21:40 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:21:40 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:21:41 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:21:41 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:21:45 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:21:45 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:21:45 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:21:47 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:21:47 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:21:53 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:21:53 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:21:53 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:21:55 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:21:55 UTC] WAA: POST data: Array
(
    [_ajax_nonce] => ede1f286a5
    [s] => checkout
    [tab] => search
    [type] => term
    [pagenow] => plugin-install
    [action] => search-install-plugins
)

[13-Aug-2025 18:21:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:21:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:21:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:22:00 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:22:00 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:22:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:22:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:22:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:22:14 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:22:14 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:22:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:22:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:22:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:22:51 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:22:51 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugin-install
    [has_focus] => false
)

[13-Aug-2025 18:22:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:22:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:22:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:22:56 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:22:56 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 18:23:53 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:23:53 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:23:53 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:23:55 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:23:55 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugin-install
    [has_focus] => true
)

[13-Aug-2025 18:23:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:23:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:23:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:23:56 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:23:56 UTC] WAA: POST data: Array
(
    [slug] => fluid-checkout
    [action] => install-plugin
    [_ajax_nonce] => ede1f286a5
    [_fs_nonce] => 
    [username] => 
    [password] => 
    [connection_type] => 
    [public_key] => 
    [private_key] => 
)

[13-Aug-2025 18:24:00 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:24:00 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:24:00 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:24:01 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:24:01 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:24:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:24:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:24:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:24:11 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:24:11 UTC] WAA: POST data: Array
(
    [slug] => fluid-checkout
    [action] => check_plugin_dependencies
    [_ajax_nonce] => ede1f286a5
    [_fs_nonce] => 
    [username] => 
    [password] => 
    [connection_type] => 
    [public_key] => 
    [private_key] => 
)

[13-Aug-2025 18:24:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:24:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:24:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:24:13 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:24:13 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:24:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:24:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:24:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:24:16 UTC] PHP Fatal error:  Uncaught Error: Call to a member function get() on null in C:\laragon\www\test\wp-content\plugins\woo-hide-shipping-by-payment\includes\class-frontend.php:202
Stack trace:
#0 C:\laragon\www\test\wp-includes\class-wp-hook.php(324): WooHideShipping_Frontend->init_payment_method(Object(WC_Checkout))
#1 C:\laragon\www\test\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#2 C:\laragon\www\test\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#3 C:\laragon\www\test\wp-content\plugins\woocommerce\includes\class-wc-checkout.php(71): do_action('woocommerce_che...', Object(WC_Checkout))
#4 C:\laragon\www\test\wp-content\plugins\woocommerce\includes\class-woocommerce.php(1169): WC_Checkout::instance()
#5 C:\laragon\www\test\wp-content\plugins\woocommerce\includes\class-woocommerce.php(202): WooCommerce->checkout()
#6 C:\laragon\www\test\wp-content\plugins\fluid-checkout\inc\checkout-steps.php(263): WooCommerce->__get('checkout')
#7 C:\laragon\www\test\wp-content\plugins\fluid-checkout\inc\checkout-steps.php(107): FluidCheckout_Steps->checkout_form_hooks()
#8 C:\laragon\www\test\wp-content\plugins\fluid-checkout\inc\checkout-steps.php(51): FluidCheckout_Steps->hooks()
#9 C:\laragon\www\test\wp-content\plugins\fluid-checkout\fluid-checkout.php(86): FluidCheckout_Steps->__construct()
#10 C:\laragon\www\test\wp-content\plugins\fluid-checkout\inc\checkout-steps.php(7125): FluidCheckout::instance()
#11 C:\laragon\www\test\wp-content\plugins\fluid-checkout\fluid-checkout.php(424): require_once('C:\\laragon\\www\\...')
#12 C:\laragon\www\test\wp-includes\class-wp-hook.php(324): FluidCheckout->load_features('')
#13 C:\laragon\www\test\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#14 C:\laragon\www\test\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#15 C:\laragon\www\test\wp-settings.php(705): do_action('after_setup_the...')
#16 C:\laragon\www\test\wp-config.php(110): require_once('C:\\laragon\\www\\...')
#17 C:\laragon\www\test\wp-load.php(50): require_once('C:\\laragon\\www\\...')
#18 C:\laragon\www\test\wp-admin\admin.php(35): require_once('C:\\laragon\\www\\...')
#19 C:\laragon\www\test\wp-admin\plugins.php(10): require_once('C:\\laragon\\www\\...')
#20 {main}
  thrown in C:\laragon\www\test\wp-content\plugins\woo-hide-shipping-by-payment\includes\class-frontend.php on line 202
[13-Aug-2025 18:24:18 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:24:18 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:24:18 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:24:18 UTC] PHP Fatal error:  Uncaught Error: Call to a member function get() on null in C:\laragon\www\test\wp-content\plugins\woo-hide-shipping-by-payment\includes\class-frontend.php:202
Stack trace:
#0 C:\laragon\www\test\wp-includes\class-wp-hook.php(324): WooHideShipping_Frontend->init_payment_method(Object(WC_Checkout))
#1 C:\laragon\www\test\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#2 C:\laragon\www\test\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#3 C:\laragon\www\test\wp-content\plugins\woocommerce\includes\class-wc-checkout.php(71): do_action('woocommerce_che...', Object(WC_Checkout))
#4 C:\laragon\www\test\wp-content\plugins\woocommerce\includes\class-woocommerce.php(1169): WC_Checkout::instance()
#5 C:\laragon\www\test\wp-content\plugins\woocommerce\includes\class-woocommerce.php(202): WooCommerce->checkout()
#6 C:\laragon\www\test\wp-content\plugins\fluid-checkout\inc\checkout-steps.php(263): WooCommerce->__get('checkout')
#7 C:\laragon\www\test\wp-content\plugins\fluid-checkout\inc\checkout-steps.php(107): FluidCheckout_Steps->checkout_form_hooks()
#8 C:\laragon\www\test\wp-content\plugins\fluid-checkout\inc\checkout-steps.php(51): FluidCheckout_Steps->hooks()
#9 C:\laragon\www\test\wp-content\plugins\fluid-checkout\fluid-checkout.php(86): FluidCheckout_Steps->__construct()
#10 C:\laragon\www\test\wp-content\plugins\fluid-checkout\inc\checkout-steps.php(7125): FluidCheckout::instance()
#11 C:\laragon\www\test\wp-content\plugins\fluid-checkout\fluid-checkout.php(424): require_once('C:\\laragon\\www\\...')
#12 C:\laragon\www\test\wp-includes\class-wp-hook.php(324): FluidCheckout->load_features('')
#13 C:\laragon\www\test\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#14 C:\laragon\www\test\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#15 C:\laragon\www\test\wp-settings.php(705): do_action('after_setup_the...')
#16 C:\laragon\www\test\wp-config.php(110): require_once('C:\\laragon\\www\\...')
#17 C:\laragon\www\test\wp-load.php(50): require_once('C:\\laragon\\www\\...')
#18 C:\laragon\www\test\wp-admin\admin.php(35): require_once('C:\\laragon\\www\\...')
#19 C:\laragon\www\test\wp-admin\plugin-install.php(16): require_once('C:\\laragon\\www\\...')
#20 {main}
  thrown in C:\laragon\www\test\wp-content\plugins\woo-hide-shipping-by-payment\includes\class-frontend.php on line 202
[13-Aug-2025 18:24:31 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:24:31 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:24:31 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:24:32 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:24:32 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:24:45 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:24:45 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:24:45 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:24:46 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:24:46 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:24:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:24:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:24:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:24:52 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:24:52 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:24:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:24:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:24:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:24:56 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:24:56 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:24:56 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:24:56 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:24:56 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:24:57 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:24:57 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 18:25:36 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:25:36 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:25:36 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:25:37 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 18:25:37 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 18:25:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:25:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:25:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:25:59 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:25:59 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => true
)

[13-Aug-2025 18:26:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:26:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:26:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:26:02 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:26:02 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:26:02 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:26:02 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:26:02 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:26:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:26:03 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => dashboard
    [has_focus] => true
)

[13-Aug-2025 18:26:04 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:26:04 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:26:04 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:26:05 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:26:05 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => true
)

[13-Aug-2025 18:26:38 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:26:38 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:26:38 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:26:39 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:26:39 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => false
)

[13-Aug-2025 18:26:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:26:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:26:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:26:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:26:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:26:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:26:59 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:26:59 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 18:26:59 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:26:59 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 18:28:02 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:28:02 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:28:02 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:28:04 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:28:04 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:28:38 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:28:38 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:28:38 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:28:39 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:28:39 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => false
)

[13-Aug-2025 18:28:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:28:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:28:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:29:01 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:29:01 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 18:30:03 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:30:03 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:30:03 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:30:05 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:30:05 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:30:39 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:30:39 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:30:39 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:30:40 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:30:40 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => false
)

[13-Aug-2025 18:31:00 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:31:00 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:31:00 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:31:02 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:31:02 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 18:32:04 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:32:04 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:32:04 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:32:06 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:32:06 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:32:40 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:32:40 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:32:40 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:32:41 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:32:41 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => false
)

[13-Aug-2025 18:33:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:33:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:33:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:33:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:33:03 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 18:33:40 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:33:40 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:33:40 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:33:41 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:33:41 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => true
)

[13-Aug-2025 18:34:05 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:34:05 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:34:05 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:34:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:34:07 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:34:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:34:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:34:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:34:53 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:34:53 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => true
)

[13-Aug-2025 18:35:02 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:35:02 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:35:02 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:35:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:35:03 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 18:35:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:35:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:35:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:35:53 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:35:53 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => false
)

[13-Aug-2025 18:36:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:36:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:36:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:36:08 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:36:08 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:37:52 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:37:52 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:37:52 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:37:54 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:37:54 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => false
)

[13-Aug-2025 18:38:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:38:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:38:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:38:08 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:38:08 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:39:53 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:39:53 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:39:53 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:39:55 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:39:55 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => false
)

[13-Aug-2025 18:40:08 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:40:08 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:40:08 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:40:09 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:40:09 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:41:54 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:41:54 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:41:54 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:41:56 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:41:56 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => false
)

[13-Aug-2025 18:42:09 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:42:09 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:42:09 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:42:10 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:42:10 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:43:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:43:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:43:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:43:57 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:43:57 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => false
)

[13-Aug-2025 18:44:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:44:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:44:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:44:11 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:44:11 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:45:15 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:45:15 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:45:15 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:45:17 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:45:17 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => true
)

[13-Aug-2025 18:46:11 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:46:11 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:46:11 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:46:13 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:46:13 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:47:15 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:47:15 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:47:15 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:47:17 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:47:17 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => false
)

[13-Aug-2025 18:48:12 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:48:12 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:48:12 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:48:14 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:48:14 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:49:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:49:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:49:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:49:18 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:49:18 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => false
)

[13-Aug-2025 18:50:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:50:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:50:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:50:15 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:50:15 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:50:47 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:50:47 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:50:47 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:50:47 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:50:47 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => true
)

[13-Aug-2025 18:51:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:51:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:51:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:51:19 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:51:19 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => false
)

[13-Aug-2025 18:51:48 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:51:48 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:51:48 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:51:48 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:51:48 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 18:52:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:52:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:52:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:52:16 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:52:16 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:53:48 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:53:48 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:53:48 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:53:50 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:53:50 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 18:54:15 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:54:15 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:54:15 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:54:16 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:54:16 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:55:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:55:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:55:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:55:51 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:55:51 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 18:56:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:56:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:56:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:56:17 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:56:17 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:57:50 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:57:50 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:57:50 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:57:52 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:57:52 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 18:58:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:58:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:58:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:58:18 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:58:18 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 18:59:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 18:59:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 18:59:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 18:59:53 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 18:59:53 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 19:00:18 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 19:00:18 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 19:00:18 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 19:00:19 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 19:00:19 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 19:01:52 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 19:01:52 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 19:01:52 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 19:01:54 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 19:01:54 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 19:02:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 19:02:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 19:02:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 19:02:20 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 19:02:20 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 19:04:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 19:04:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 19:04:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 19:04:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 19:04:22 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 20:50:18 UTC] WooHideShipping Debug - Chosen payment method: cheque
[13-Aug-2025 20:50:18 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 20:50:18 UTC] WooHideShipping Debug - Found rules: 0
[13-Aug-2025 20:50:18 UTC] WooHideShipping Debug - No rules found for payment method: cheque
[13-Aug-2025 20:50:18 UTC] WooHideShipping Debug - Chosen payment method: cheque
[13-Aug-2025 20:50:18 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 20:50:18 UTC] WooHideShipping Debug - Found rules: 0
[13-Aug-2025 20:50:18 UTC] WooHideShipping Debug - No rules found for payment method: cheque
[13-Aug-2025 20:50:19 UTC] WooHideShipping Debug - Chosen payment method: cheque
[13-Aug-2025 20:50:19 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 20:50:19 UTC] WooHideShipping Debug - Found rules: 0
[13-Aug-2025 20:50:19 UTC] WooHideShipping Debug - No rules found for payment method: cheque
[13-Aug-2025 20:50:21 UTC] WooHideShipping Debug - Chosen payment method: cheque
[13-Aug-2025 20:50:21 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[13-Aug-2025 20:50:21 UTC] WooHideShipping Debug - Found rules: 0
[13-Aug-2025 20:50:21 UTC] WooHideShipping Debug - No rules found for payment method: cheque
[13-Aug-2025 20:55:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 20:55:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 20:55:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 20:55:18 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 20:55:18 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 20:55:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 20:55:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 20:55:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 20:55:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 20:55:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 20:55:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 20:55:22 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 20:55:22 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 20:55:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 20:55:22 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => true
)

[14-Aug-2025 07:16:01 UTC] Automatic updates starting...
[14-Aug-2025 07:16:02 UTC]   Automatic plugin updates starting...
[14-Aug-2025 07:16:02 UTC]   Automatic plugin updates complete.
[14-Aug-2025 07:16:02 UTC]   Automatic theme updates starting...
[14-Aug-2025 07:16:02 UTC]   Automatic theme updates complete.
[14-Aug-2025 07:16:02 UTC] Automatic updates complete.
[14-Aug-2025 07:16:05 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:16:05 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:16:05 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:16:06 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:16:06 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:16:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:16:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:16:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:16:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:16:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:16:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Chosen payment method: bacs
[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Found rules: 1
[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[14-Aug-2025 07:16:08 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:16:08 UTC] WAA: POST data: Array
(
    [_wpnonce] => 342ec5f1ca
    [timezone] => Europe/Rome
    [action] => get-community-events
)

[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Chosen payment method: bacs
[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Found rules: 1
[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[14-Aug-2025 07:16:08 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[14-Aug-2025 07:18:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:18:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:18:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:18:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:18:03 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => dashboard
    [has_focus] => true
)

[14-Aug-2025 07:18:05 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:18:05 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:18:05 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:18:05 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:18:05 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:18:12 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:18:12 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:18:12 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:18:13 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:18:13 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:18:13 UTC] WordPress database error Duplicate key name 'slug_cronjob' for query CREATE TABLE wp_wcos_cronjobs (
            id int(11) NOT NULL AUTO_INCREMENT,
            nome_cronjob varchar(255) NOT NULL,
            slug_cronjob varchar(255) NOT NULL UNIQUE,
            cadenza_periodica varchar(50) NOT NULL,
            numero_giorni int(11) NOT NULL,
            status_da_aggiornare text NOT NULL,
            nuovo_status varchar(50) NOT NULL,
            oggetto_email varchar(500) NOT NULL,
            testo_email text NOT NULL,
            attivo tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY slug_cronjob (slug_cronjob),
            KEY attivo (attivo)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci made by activate_plugin, do_action('activate_woo-cronjob-order-status/woo-cronjob-order-status.php'), WP_Hook->do_action, WP_Hook->apply_filters, wcos_activate, Woo_Cronjob_DB->create_tables, dbDelta
[14-Aug-2025 07:18:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:18:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:18:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:18:14 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-admin/admin.php'), require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:18:16 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:18:16 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:18:16 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:18:16 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by do_action_ref_array('wcos_check_cronjobs'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->check_and_execute_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:18:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:18:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:18:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:18:20 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-admin/admin.php'), require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:18:21 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:18:21 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:18:21 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs  ORDER BY created_at DESC made by require_once('wp-admin/admin.php'), do_action('tools_page_wcos-cronjobs'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Admin->render_admin_page, Woo_Cronjob_Admin->render_cronjob_list, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:18:22 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:18:22 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:18:22 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:18:23 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-admin/admin.php'), require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:18:23 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:18:23 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:19:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:19:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:19:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:19:24 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:19:25 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:19:25 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:19:25 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => true
)

[14-Aug-2025 07:20:04 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:20:04 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:20:04 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:20:04 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:20:06 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:20:06 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:20:06 UTC] WAA: POST data: Array
(
    [action] => wcos_save_cronjob
    [wcos_nonce] => fdfc24a65a
    [_wp_http_referer] => /wp-admin/tools.php?page=wcos-cronjobs&action=new
    [nome_cronjob] => da in sospeso
    [slug_cronjob] => da-in-sospeso
    [cadenza_periodica] => daily
    [numero_giorni] => 7
    [status_da_aggiornare] => Array
        (
            [0] => wc-on-hold
        )

    [nuovo_status] => wc-processing
    [oggetto_email] => ordine in lavorazione
    [testo_email] => Gentile cliente {customer_first_name}{customer_first_name}{customer_last_name} il suo ordine{order_number} è in lavorazione
    [attivo] => 1
    [submit] => Crea CronJob
)

[14-Aug-2025 07:20:06 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT COUNT(*) FROM wp_wcos_cronjobs WHERE slug_cronjob = 'da-in-sospeso' made by do_action('admin_post_wcos_save_cronjob'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Admin->handle_save_cronjob, Woo_Cronjob_Admin->validate_cronjob_data, Woo_Cronjob_DB->slug_exists
[14-Aug-2025 07:20:06 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SHOW FULL COLUMNS FROM `wp_wcos_cronjobs` made by do_action('admin_post_wcos_save_cronjob'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Admin->handle_save_cronjob, Woo_Cronjob_DB->insert_cronjob
[14-Aug-2025 07:20:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:20:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:20:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:20:07 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-admin/admin.php'), require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:20:07 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:20:07 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:20:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:20:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:20:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:20:14 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-admin/admin.php'), require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:20:15 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:20:15 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:20:15 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:20:15 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs  ORDER BY created_at DESC made by require_once('wp-admin/admin.php'), do_action('tools_page_wcos-cronjobs'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Admin->render_admin_page, Woo_Cronjob_Admin->render_cronjob_list, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:20:18 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:20:18 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:20:18 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:20:18 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-admin/admin.php'), require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:20:18 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:20:18 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:21:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:21:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:21:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:21:20 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:21:21 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:21:21 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:21:21 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 07:23:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:23:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:23:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:23:20 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:23:21 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:23:21 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:23:21 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 07:25:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:25:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:25:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:25:17 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:25:18 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:25:19 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:25:19 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => true
)

[14-Aug-2025 07:25:24 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:25:29 UTC] WordPress database error Table 'test.wp_wcos_cronjobs' doesn't exist for query SELECT * FROM wp_wcos_cronjobs WHERE attivo = 1 ORDER BY created_at DESC made by require_once('wp-load.php'), require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, Woo_Cronjob_Cron->register_individual_cronjobs, Woo_Cronjob_DB->get_all_cronjobs
[14-Aug-2025 07:25:47 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:25:47 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:25:47 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:25:47 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:25:47 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:25:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:25:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:25:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:25:51 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:25:51 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:26:22 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:26:22 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:26:22 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:26:23 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:26:23 UTC] WAA: POST data: Array
(
    [action] => wcos_save_cronjob
    [wcos_nonce] => fdfc24a65a
    [_wp_http_referer] => /wp-admin/tools.php?page=wcos-cronjobs&action=new
    [nome_cronjob] => test-cron
    [slug_cronjob] => test-cron
    [cadenza_periodica] => daily
    [numero_giorni] => 7
    [status_da_aggiornare] => Array
        (
            [0] => wc-on-hold
        )

    [nuovo_status] => wc-processing
    [oggetto_email] => test cron
    [testo_email] => testiamo il cron job
    [attivo] => 1
    [submit] => Crea CronJob
)

[14-Aug-2025 07:26:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:26:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:26:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:26:25 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:26:25 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:26:52 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:26:52 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:26:52 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:26:53 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:26:53 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:27:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:27:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:27:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:27:56 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:27:56 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 07:29:56 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:29:56 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:29:56 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:29:57 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:29:57 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 07:31:53 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:31:53 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:31:53 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:31:54 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:31:54 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => true
)

[14-Aug-2025 07:31:56 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:31:56 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:31:56 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:31:56 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:31:56 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:31:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:31:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:31:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:32:00 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:32:00 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:32:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:32:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:32:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:32:11 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:32:11 UTC] WAA: POST data: Array
(
    [action] => wcos_save_cronjob
    [cronjob_id] => 1
    [wcos_nonce] => fdfc24a65a
    [_wp_http_referer] => /wp-admin/tools.php?page=wcos-cronjobs&action=edit&id=1
    [nome_cronjob] => test-cron
    [slug_cronjob] => test-cron
    [cadenza_periodica] => daily
    [numero_giorni] => 7
    [status_da_aggiornare] => Array
        (
            [0] => wc-on-hold
        )

    [nuovo_status] => wc-processing
    [oggetto_email] => test cron
    [testo_email] => testiamo il cron job{order_number} {order_status}
    [attivo] => 1
    [submit] => Aggiorna CronJob
)

[14-Aug-2025 07:32:11 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:32:11 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:32:11 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:32:13 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:32:13 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:33:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:33:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:33:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:33:15 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:33:15 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 07:34:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:34:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:34:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:34:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:34:22 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => true
)

[14-Aug-2025 07:34:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:34:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:34:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:34:24 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:34:24 UTC] WAA: POST data: Array
(
    [0] => Array
        (
            [task] => trash_pending_orders
        )

    [1] => Array
        (
            [task] => trash_failed_orders
        )

    [2] => Array
        (
            [task] => trash_cancelled_orders
        )

    [3] => Array
        (
            [task] => anonymize_refunded_orders
        )

    [4] => Array
        (
            [task] => anonymize_completed_orders
        )

    [5] => Array
        (
            [task] => delete_inactive_accounts
        )

)

[14-Aug-2025 07:35:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:35:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:35:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:35:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:35:22 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => true
)

[14-Aug-2025 07:36:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:36:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:36:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:36:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:36:22 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 07:37:18 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:37:18 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:37:18 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:37:20 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:37:20 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:37:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:37:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:37:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:37:21 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:37:21 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => true
)

[14-Aug-2025 07:38:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:38:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:38:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:38:23 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:38:23 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 07:38:44 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:38:44 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:38:44 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:38:45 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:38:45 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:38:45 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:38:46 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:38:46 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:38:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:38:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:38:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:38:52 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:38:52 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:38:52 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-on-hold
[14-Aug-2025 07:38:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 07:38:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:38:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-on-hold
[14-Aug-2025 07:38:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:38:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-on-hold
[14-Aug-2025 07:38:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[14-Aug-2025 07:38:52 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-on-hold, Pulsanti trovati: 2
[14-Aug-2025 07:38:53 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:38:53 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:38:53 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:38:55 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:38:55 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 07:38:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:38:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:38:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:38:58 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:38:58 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:39:04 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:39:04 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:39:04 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:39:04 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:39:04 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[14-Aug-2025 07:39:05 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:39:05 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:39:05 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:39:06 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:39:06 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:39:06 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:39:06 UTC] WAA: POST data: Array
(
    [_wpnonce] => 298289d2bb
    [_wp_http_referer] => /wp-admin/post.php?post=29037&action=edit
    [user_ID] => 1
    [action] => editpost
    [originalaction] => editpost
    [post_author] => 1
    [post_type] => shop_order
    [original_post_status] => wc-on-hold
    [referredby] => http://test.test/wp-admin/edit.php?post_type=shop_order
    [_wp_original_http_referer] => http://test.test/wp-admin/edit.php?post_type=shop_order
    [post_ID] => 29037
    [meta-box-order-nonce] => 54b161f2d4
    [closedpostboxesnonce] => ca55cf2539
    [original_post_title] => Order &ndash; Agosto 13, 2025 @ 04:00 PM
    [post_title] => Ordine
    [samplepermalinknonce] => 788affc5c7
    [wc_order_action] => 
    [save] => Aggiorna
    [order_note] => 
    [order_note_type] => 
    [woocommerce_meta_nonce] => 22bf455e96
    [post_status] => on-hold
    [order_date] => 2025-08-01
    [order_date_hour] => 16
    [order_date_minute] => 00
    [order_date_second] => 56
    [order_status] => wc-on-hold
    [customer_user] => 1
    [_billing_first_name] => Giovanni
    [_billing_last_name] => Castaldo
    [_billing_company] => 
    [_billing_address_1] => via Giotto 13
    [_billing_address_2] => 
    [_billing_city] => Caserta
    [_billing_postcode] => 81100
    [_billing_country] => IT
    [_billing_state] => CE
    [_billing_email] => <EMAIL>
    [_billing_phone] => 
    [_payment_method] => bacs
    [_transaction_id] => 
    [_shipping_first_name] => Giovanni
    [_shipping_last_name] => Castaldo
    [_shipping_company] => 
    [_shipping_address_1] => via Giotto 13
    [_shipping_address_2] => 
    [_shipping_city] => Caserta
    [_shipping_postcode] => 81100
    [_shipping_country] => IT
    [_shipping_state] => CE
    [_shipping_phone] => 
    [customer_note] => 
    [order_item_id] => Array
        (
            [0] => 8
        )

    [order_item_tax_class] => Array
        (
            [8] => 
        )

    [order_item_qty] => Array
        (
            [8] => 2
        )

    [refund_order_item_qty] => Array
        (
            [8] => 
        )

    [line_subtotal] => Array
        (
            [8] => 858
        )

    [line_total] => Array
        (
            [8] => 858
        )

    [refund_line_total] => Array
        (
            [8] => 
        )

    [order_refund_id] => Array
        (
            [0] => 29038
        )

    [restock_refunded_items] => on
    [refund_amount] => 
    [refund_reason] => 
    [refunded_amount] => 858
    [meta] => Array
        (
            [3949] => Array
                (
                    [key] => is_vat_exempt
                    [value] => no
                )

        )

    [_ajax_nonce] => 59214f2e5e
    [metakeyselect] => #NONE#
    [metakeyinput] => 
    [metavalue] => 
    [_ajax_nonce-add-meta] => 628a6549ec
)

[14-Aug-2025 07:39:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:39:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:39:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:39:07 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:39:07 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:39:08 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:39:08 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:39:08 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:39:09 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:39:09 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 298289d2bb
    [post_ID] => 29037
    [active_post_lock] => 1755157138:1
)

[14-Aug-2025 07:39:09 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:39:09 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:39:09 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:39:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:39:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:39:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:39:11 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:39:11 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:39:12 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:39:12 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:39:12 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:39:12 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:39:12 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 298289d2bb
    [post_ID] => 29037
    [active_post_lock] => 1755157147:1
)

[14-Aug-2025 07:39:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:39:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:39:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:39:14 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:39:14 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:39:18 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:39:18 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:39:18 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:39:19 UTC] PHP Fatal error:  Uncaught ArgumentCountError: Too few arguments to function Woo_Cronjob_Email::get_default_placeholders(), 3 passed in C:\laragon\www\test\wp-includes\class-wp-hook.php on line 326 and exactly 4 expected in C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\includes\class-woo-cronjob-email.php:229
Stack trace:
#0 C:\laragon\www\test\wp-includes\class-wp-hook.php(326): Woo_Cronjob_Email->get_default_placeholders(Array, Object(Automattic\WooCommerce\Admin\Overrides\Order), 'on-hold')
#1 C:\laragon\www\test\wp-includes\plugin.php(205): WP_Hook->apply_filters(Array, Array)
#2 C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\includes\class-woo-cronjob-email.php(101): apply_filters('wcos_email_plac...', Array, Object(Automattic\WooCommerce\Admin\Overrides\Order), 'on-hold', 'processing')
#3 C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\includes\class-woo-cronjob-email.php(64): Woo_Cronjob_Email->get_placeholders(Object(Automattic\WooCommerce\Admin\Overrides\Order), 'on-hold', 'processing')
#4 C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\includes\class-woo-cronjob-email.php(30): Woo_Cronjob_Email->replace_placeholders('test cron', Object(Automattic\WooCommerce\Admin\Overrides\Order), 'on-hold', 'processing')
#5 C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\includes\class-woo-cronjob-cron.php(111): Woo_Cronjob_Email->send_customer_notification(Object(Automattic\WooCommerce\Admin\Overrides\Order), 'test cron', 'testiamo il cro...', 'on-hold', 'processing')
#6 C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\includes\class-woo-cronjob-cron.php(203): Woo_Cronjob_Cron->execute_cronjob(Object(stdClass))
#7 C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\includes\class-woo-cronjob-admin.php(252): Woo_Cronjob_Cron->manual_execute(1)
#8 C:\laragon\www\test\wp-includes\class-wp-hook.php(324): Woo_Cronjob_Admin->handle_run_cronjob('')
#9 C:\laragon\www\test\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#10 C:\laragon\www\test\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#11 C:\laragon\www\test\wp-admin\admin-post.php(82): do_action('admin_post_wcos...')
#12 {main}
  thrown in C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\includes\class-woo-cronjob-email.php on line 229
[14-Aug-2025 07:39:29 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:39:29 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:39:29 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:39:29 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:39:29 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:39:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:39:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:39:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:39:33 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:39:33 UTC] WAA: POST data: Array
(
)

[14-Aug-2025 07:39:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:39:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:39:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:39:34 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:39:34 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:39:38 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:39:38 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:39:38 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:39:38 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:39:38 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:40:40 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:40:40 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:40:40 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:40:41 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:40:41 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => false
)

[14-Aug-2025 07:41:05 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:41:05 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:41:05 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:41:06 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:41:06 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[14-Aug-2025 07:42:41 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:42:41 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:42:41 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:42:42 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:42:42 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-status
    [has_focus] => false
)

[14-Aug-2025 07:43:01 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method Automattic\WooCommerce\Admin\Overrides\OrderRefund::get_billing_first_name() in C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\debug-cronjob.php:127
Stack trace:
#0 {main}
  thrown in C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\debug-cronjob.php on line 127
[14-Aug-2025 07:43:04 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method Automattic\WooCommerce\Admin\Overrides\OrderRefund::get_billing_first_name() in C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\debug-cronjob.php:127
Stack trace:
#0 {main}
  thrown in C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\debug-cronjob.php on line 127
[14-Aug-2025 07:43:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:43:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:43:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:43:06 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:43:06 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[14-Aug-2025 07:43:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:43:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:43:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:43:26 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:43:26 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 07:43:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:43:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:43:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:43:29 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:43:29 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:43:29 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[14-Aug-2025 07:43:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 07:43:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:43:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-processing
[14-Aug-2025 07:43:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:43:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[14-Aug-2025 07:43:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[14-Aug-2025 07:43:29 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 2
[14-Aug-2025 07:43:30 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:43:30 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:43:30 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:43:31 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:43:31 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 07:43:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:43:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:43:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:43:33 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:43:33 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:43:37 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:43:37 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:43:37 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:43:38 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:43:38 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:43:38 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:43:38 UTC] WAA: POST data: Array
(
    [_wpnonce] => 298289d2bb
    [_wp_http_referer] => /wp-admin/post.php?post=29037&action=edit
    [user_ID] => 1
    [action] => editpost
    [originalaction] => editpost
    [post_author] => 1
    [post_type] => shop_order
    [original_post_status] => wc-processing
    [referredby] => http://test.test/wp-admin/edit.php?post_type=shop_order
    [_wp_original_http_referer] => http://test.test/wp-admin/edit.php?post_type=shop_order
    [post_ID] => 29037
    [meta-box-order-nonce] => 54b161f2d4
    [closedpostboxesnonce] => ca55cf2539
    [original_post_title] => Order &ndash; Agosto 1, 2025 @ 04:00 PM
    [post_title] => Ordine
    [samplepermalinknonce] => 788affc5c7
    [wc_order_action] => 
    [save] => Aggiorna
    [order_note] => 
    [order_note_type] => 
    [woocommerce_meta_nonce] => 22bf455e96
    [post_status] => processing
    [order_date] => 2025-08-01
    [order_date_hour] => 16
    [order_date_minute] => 00
    [order_date_second] => 56
    [order_status] => wc-on-hold
    [customer_user] => 1
    [_billing_first_name] => Giovanni
    [_billing_last_name] => Castaldo
    [_billing_company] => 
    [_billing_address_1] => via Giotto 13
    [_billing_address_2] => 
    [_billing_city] => Caserta
    [_billing_postcode] => 81100
    [_billing_country] => IT
    [_billing_state] => CE
    [_billing_email] => <EMAIL>
    [_billing_phone] => 
    [_payment_method] => bacs
    [_transaction_id] => 
    [_shipping_first_name] => Giovanni
    [_shipping_last_name] => Castaldo
    [_shipping_company] => 
    [_shipping_address_1] => via Giotto 13
    [_shipping_address_2] => 
    [_shipping_city] => Caserta
    [_shipping_postcode] => 81100
    [_shipping_country] => IT
    [_shipping_state] => CE
    [_shipping_phone] => 
    [customer_note] => 
    [order_item_id] => Array
        (
            [0] => 8
        )

    [order_item_tax_class] => Array
        (
            [8] => 
        )

    [order_item_qty] => Array
        (
            [8] => 2
        )

    [refund_order_item_qty] => Array
        (
            [8] => 
        )

    [line_subtotal] => Array
        (
            [8] => 858
        )

    [line_total] => Array
        (
            [8] => 858
        )

    [refund_line_total] => Array
        (
            [8] => 
        )

    [order_refund_id] => Array
        (
            [0] => 29038
        )

    [restock_refunded_items] => on
    [refund_amount] => 
    [refund_reason] => 
    [refunded_amount] => 858
    [meta] => Array
        (
            [3949] => Array
                (
                    [key] => is_vat_exempt
                    [value] => no
                )

        )

    [_ajax_nonce] => 59214f2e5e
    [metakeyselect] => #NONE#
    [metakeyinput] => 
    [metavalue] => 
    [_ajax_nonce-add-meta] => 628a6549ec
)

[14-Aug-2025 07:43:38 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:43:38 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:43:38 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:43:39 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:43:39 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:43:40 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:43:40 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:43:40 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:43:40 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:43:40 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:43:40 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:43:41 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:43:41 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 298289d2bb
    [post_ID] => 29037
    [active_post_lock] => 1755157413:1
)

[14-Aug-2025 07:43:47 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:43:47 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:43:47 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:43:48 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:43:48 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:43:48 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:43:48 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:43:48 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:43:48 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:43:48 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:43:48 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:43:49 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:43:49 UTC] WAA: POST data: Array
(
)

[14-Aug-2025 07:43:49 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:43:49 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 298289d2bb
    [post_ID] => 29037
    [active_post_lock] => 1755157419:1
)

[14-Aug-2025 07:43:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:43:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:43:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:43:54 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:43:54 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:43:54 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:43:55 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:43:55 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:43:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:43:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:43:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:44:00 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:44:00 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:44:00 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[14-Aug-2025 07:44:00 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 07:44:00 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:44:00 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-processing
[14-Aug-2025 07:44:00 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:44:00 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[14-Aug-2025 07:44:00 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[14-Aug-2025 07:44:00 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 2
[14-Aug-2025 07:44:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:44:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:44:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:44:02 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:44:02 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 07:44:11 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:44:11 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:44:11 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:44:11 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:44:11 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 07:44:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:44:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:44:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:44:17 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:44:17 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:44:22 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:44:22 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:44:22 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:44:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:44:22 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[14-Aug-2025 07:44:30 UTC] WooHideShipping Debug - Chosen payment method: bacs
[14-Aug-2025 07:44:30 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[14-Aug-2025 07:44:30 UTC] WooHideShipping Debug - Found rules: 1
[14-Aug-2025 07:44:30 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:44:30 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:44:30 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:44:30 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[14-Aug-2025 07:44:30 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:44:30 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[14-Aug-2025 07:44:30 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:44:30 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[14-Aug-2025 07:44:30 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[14-Aug-2025 07:44:30 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[14-Aug-2025 07:44:32 UTC] WooHideShipping Debug - Set default payment method: bacs
[14-Aug-2025 07:44:32 UTC] WooHideShipping Debug - Chosen payment method: bacs
[14-Aug-2025 07:44:32 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[14-Aug-2025 07:44:32 UTC] WooHideShipping Debug - Found rules: 1
[14-Aug-2025 07:44:32 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:44:32 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:44:32 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:44:32 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[14-Aug-2025 07:44:32 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:44:32 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[14-Aug-2025 07:44:32 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:44:32 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[14-Aug-2025 07:44:32 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[14-Aug-2025 07:44:32 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[14-Aug-2025 07:44:33 UTC] WooHideShipping Debug - Chosen payment method: bacs
[14-Aug-2025 07:44:33 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[14-Aug-2025 07:44:33 UTC] WooHideShipping Debug - Found rules: 1
[14-Aug-2025 07:44:33 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:44:33 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:44:33 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:44:33 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[14-Aug-2025 07:44:33 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:44:33 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[14-Aug-2025 07:44:33 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:44:33 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[14-Aug-2025 07:44:33 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[14-Aug-2025 07:44:33 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[14-Aug-2025 07:44:48 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:44:48 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:44:48 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:44:48 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:44:48 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 07:44:50 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:44:50 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:44:50 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:44:52 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:44:52 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:44:52 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-on-hold
[14-Aug-2025 07:44:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 07:44:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:44:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-on-hold
[14-Aug-2025 07:44:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:44:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-on-hold
[14-Aug-2025 07:44:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[14-Aug-2025 07:44:52 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29040, Status: wc-on-hold, Pulsanti trovati: 2
[14-Aug-2025 07:44:52 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[14-Aug-2025 07:44:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 07:44:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:44:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-processing
[14-Aug-2025 07:44:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:44:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[14-Aug-2025 07:44:52 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[14-Aug-2025 07:44:52 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 2
[14-Aug-2025 07:44:54 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:44:54 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:44:54 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:44:54 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:44:54 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29040
                    [1] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 07:45:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:03 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:03 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:03 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:03 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:03 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:03 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:45:03 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29040
                    [1] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 07:45:04 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:45:04 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-completed
[14-Aug-2025 07:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 07:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro NO MATCH per status wc-completed
[14-Aug-2025 07:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato NO MATCH per status wc-completed
[14-Aug-2025 07:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 0
[14-Aug-2025 07:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29040, Status: wc-completed, Pulsanti trovati: 0
[14-Aug-2025 07:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[14-Aug-2025 07:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 07:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-processing
[14-Aug-2025 07:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[14-Aug-2025 07:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[14-Aug-2025 07:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 2
[14-Aug-2025 07:45:05 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:05 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:05 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:05 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:45:05 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29040
                    [1] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 07:45:13 UTC] WooHideShipping Debug - Chosen payment method: bacs
[14-Aug-2025 07:45:13 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[14-Aug-2025 07:45:13 UTC] WooHideShipping Debug - Found rules: 1
[14-Aug-2025 07:45:13 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:45:13 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:45:13 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:45:13 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[14-Aug-2025 07:45:13 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:45:13 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[14-Aug-2025 07:45:13 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:45:13 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[14-Aug-2025 07:45:13 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[14-Aug-2025 07:45:13 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[14-Aug-2025 07:45:15 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:15 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:15 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:15 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:45:15 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29040
                    [1] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Chosen payment method: bacs
[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Found rules: 1
[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Chosen payment method: bacs
[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Available rates: Array
(
    [0] => free_shipping:1
    [1] => flat_rate:2
    [2] => flat_rate:3
)

[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Found rules: 1
[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Rule "Test Rule" wants to hide: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Methods to hide: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Checking rate: free_shipping:1 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - No match found for rate: free_shipping:1
[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Checking rate: flat_rate:2 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - No match found for rate: flat_rate:2
[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Checking rate: flat_rate:3 against methods: Array
(
    [0] => flat_rate:3
)

[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Exact match found: flat_rate:3 === flat_rate:3
[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Hiding shipping method: flat_rate:3 (Spedizione assicurata)
[14-Aug-2025 07:45:15 UTC] WooHideShipping Debug - Rates before: 3, after: 2
[14-Aug-2025 07:45:25 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:25 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:25 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:25 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:45:25 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29040
                    [1] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 07:45:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:27 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:45:27 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-on-hold
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-on-hold
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-on-hold
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29041, Status: wc-on-hold, Pulsanti trovati: 2
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-completed
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro NO MATCH per status wc-completed
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato NO MATCH per status wc-completed
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 0
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29040, Status: wc-completed, Pulsanti trovati: 0
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-processing
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[14-Aug-2025 07:45:27 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 2
[14-Aug-2025 07:45:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:29 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:45:29 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29041
                    [1] => post-29040
                    [2] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 07:45:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:33 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:45:33 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:45:36 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:36 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:36 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:37 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:37 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:37 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:38 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:45:38 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:45:41 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:41 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:41 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:42 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:45:42 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-on-hold
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-on-hold
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-on-hold
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29041, Status: wc-on-hold, Pulsanti trovati: 2
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-completed
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro NO MATCH per status wc-completed
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato NO MATCH per status wc-completed
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 0
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29040, Status: wc-completed, Pulsanti trovati: 0
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-processing
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[14-Aug-2025 07:45:42 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 2
[14-Aug-2025 07:45:44 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:44 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:44 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:45 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:45:45 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29041
                    [1] => post-29040
                    [2] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 07:45:46 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:46 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:46 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:46 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:45:46 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:45:52 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:52 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:52 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:53 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:45:53 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:45:53 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:45:53 UTC] WAA: POST data: Array
(
    [_wpnonce] => 8ea0b3f072
    [_wp_http_referer] => /wp-admin/post.php?post=29041&action=edit
    [user_ID] => 1
    [action] => editpost
    [originalaction] => editpost
    [post_author] => 1
    [post_type] => shop_order
    [original_post_status] => wc-on-hold
    [referredby] => http://test.test/wp-admin/edit.php?post_type=shop_order
    [_wp_original_http_referer] => http://test.test/wp-admin/edit.php?post_type=shop_order
    [post_ID] => 29041
    [meta-box-order-nonce] => 54b161f2d4
    [closedpostboxesnonce] => ca55cf2539
    [original_post_title] => Order &ndash; Agosto 14, 2025 @ 09:45 AM
    [post_title] => Ordine
    [samplepermalinknonce] => 788affc5c7
    [wc_order_action] => 
    [save] => Aggiorna
    [order_note] => 
    [order_note_type] => 
    [woocommerce_meta_nonce] => 22bf455e96
    [post_status] => on-hold
    [order_date] => 2025-08-01
    [order_date_hour] => 09
    [order_date_minute] => 45
    [order_date_second] => 15
    [order_status] => wc-on-hold
    [customer_user] => 1
    [_billing_first_name] => Giovanni
    [_billing_last_name] => Castaldo
    [_billing_company] => 
    [_billing_address_1] => via Giotto 13
    [_billing_address_2] => 
    [_billing_city] => Caserta
    [_billing_postcode] => 81100
    [_billing_country] => IT
    [_billing_state] => CE
    [_billing_email] => <EMAIL>
    [_billing_phone] => 
    [_payment_method] => bacs
    [_transaction_id] => 
    [_shipping_first_name] => Giovanni
    [_shipping_last_name] => Castaldo
    [_shipping_company] => 
    [_shipping_address_1] => via Giotto 13
    [_shipping_address_2] => 
    [_shipping_city] => Caserta
    [_shipping_postcode] => 81100
    [_shipping_country] => IT
    [_shipping_state] => CE
    [_shipping_phone] => 
    [customer_note] => 
    [order_item_id] => Array
        (
            [0] => 14
        )

    [order_item_tax_class] => Array
        (
            [14] => 
        )

    [order_item_qty] => Array
        (
            [14] => 1
        )

    [refund_order_item_qty] => Array
        (
            [14] => 
        )

    [line_subtotal] => Array
        (
            [14] => 299
        )

    [line_total] => Array
        (
            [14] => 299
        )

    [refund_line_total] => Array
        (
            [14] => 
            [15] => 
        )

    [shipping_method_id] => Array
        (
            [0] => 15
        )

    [shipping_method_title] => Array
        (
            [15] => Spedizione gratuita
        )

    [shipping_method] => Array
        (
            [15] => free_shipping
        )

    [meta_key] => Array
        (
            [15] => Array
                (
                    [132] => Prodotti
                )

        )

    [meta_value] => Array
        (
            [15] => Array
                (
                    [132] => Classic wooden chair &times; 1
                )

        )

    [shipping_cost] => Array
        (
            [15] => 0
        )

    [restock_refunded_items] => on
    [refund_amount] => 
    [refund_reason] => 
    [refunded_amount] => 0
    [meta] => Array
        (
            [4329] => Array
                (
                    [key] => is_vat_exempt
                    [value] => no
                )

        )

    [_ajax_nonce] => 59214f2e5e
    [metakeyselect] => #NONE#
    [metakeyinput] => 
    [metavalue] => 
    [_ajax_nonce-add-meta] => 628a6549ec
)

[14-Aug-2025 07:45:54 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:54 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:54 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:54 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:45:54 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:45:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:56 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:45:56 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:45:56 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:45:56 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:45:56 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 8ea0b3f072
    [post_ID] => 29041
    [active_post_lock] => 1755157546:1
)

[14-Aug-2025 07:46:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:46:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:46:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:46:02 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:46:02 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:46:03 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:46:03 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:46:03 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:46:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:46:03 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 8ea0b3f072
    [post_ID] => 29041
    [active_post_lock] => 1755157554:1
)

[14-Aug-2025 07:46:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:46:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:46:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:46:15 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:46:15 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:46:18 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:46:18 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:46:18 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:46:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:46:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:46:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:46:20 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:46:20 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:46:23 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:46:23 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-completed
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro NO MATCH per status wc-completed
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato NO MATCH per status wc-completed
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 0
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29040, Status: wc-completed, Pulsanti trovati: 0
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-processing
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 2
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-processing
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[14-Aug-2025 07:46:23 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29041, Status: wc-processing, Pulsanti trovati: 2
[14-Aug-2025 07:46:24 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:46:24 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:46:24 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:46:25 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:46:25 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29040
                    [1] => post-29037
                    [2] => post-29041
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 07:46:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:46:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:46:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:46:28 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:46:28 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:46:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:46:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:46:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:46:33 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:46:33 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:47:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:47:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:47:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:47:36 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:47:36 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 07:49:36 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:49:36 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:49:36 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:49:37 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:49:37 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 07:50:41 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:50:41 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:50:41 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:50:43 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:50:43 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => true
)

[14-Aug-2025 07:50:43 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:50:43 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:50:43 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:50:44 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:50:44 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:51:44 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:51:44 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:51:44 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:51:46 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:51:46 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => true
)

[14-Aug-2025 07:52:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:52:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:52:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:52:12 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:52:12 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:53:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:53:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:53:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:53:15 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:53:15 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 07:55:15 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:55:15 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:55:15 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:55:16 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:55:16 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 07:57:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:57:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:57:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:57:17 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:57:17 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 07:59:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:59:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:59:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:59:18 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 07:59:18 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 07:59:53 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:59:53 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:59:53 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:59:55 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:59:55 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 07:59:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 07:59:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 07:59:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 07:59:59 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 07:59:59 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:00:04 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:00:04 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:00:04 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:00:05 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:00:05 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:00:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:00:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:00:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:00:06 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:00:06 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:00:11 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:00:11 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:00:11 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:00:12 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:00:12 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:00:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:00:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:00:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:00:15 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:00:15 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:00:29 UTC] WordPress database error Can't DROP 'numero_giorni'; check that column/key exists for query ALTER TABLE `wp_wcos_cronjobs` DROP COLUMN `numero_giorni`
[14-Aug-2025 08:00:32 UTC] WordPress database error Can't DROP 'numero_giorni'; check that column/key exists for query ALTER TABLE `wp_wcos_cronjobs` DROP COLUMN `numero_giorni`
[14-Aug-2025 08:00:36 UTC] WordPress database error Can't DROP 'numero_giorni'; check that column/key exists for query ALTER TABLE `wp_wcos_cronjobs` DROP COLUMN `numero_giorni`
[14-Aug-2025 08:00:39 UTC] WordPress database error Can't DROP 'numero_giorni'; check that column/key exists for query ALTER TABLE `wp_wcos_cronjobs` DROP COLUMN `numero_giorni`
[14-Aug-2025 08:00:39 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:00:39 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:00:39 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:00:40 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:00:40 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:00:41 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:00:41 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:00:41 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:00:42 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 08:00:42 UTC] WAA: POST data: Array
(
)

[14-Aug-2025 08:00:42 UTC] WordPress database error Can't DROP 'numero_giorni'; check that column/key exists for query ALTER TABLE `wp_wcos_cronjobs` DROP COLUMN `numero_giorni`
[14-Aug-2025 08:00:46 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:00:46 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:00:46 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:00:47 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:00:47 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:00:47 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:00:48 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:00:48 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:00:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:00:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:00:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:00:51 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:00:51 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:01:30 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:01:30 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:01:30 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:01:31 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 08:01:31 UTC] WAA: POST data: Array
(
    [action] => wcos_save_cronjob
    [wcos_nonce] => fdfc24a65a
    [_wp_http_referer] => /wp-admin/tools.php?page=wcos-cronjobs&action=new
    [nome_cronjob] => in sospeso
    [slug_cronjob] => in-sospeso
    [cadenza_periodica] => daily
    [giorni_da] => 7
    [giorni_a] => 14
    [status_da_aggiornare] => Array
        (
            [0] => wc-pending
            [1] => wc-processing
            [2] => wc-on-hold
        )

    [nuovo_status] => wc-completed
    [oggetto_email] => test email
    [testo_email] => ordine {order_number} aggiornato
    [attivo] => 1
    [submit] => Crea CronJob
)

[14-Aug-2025 08:01:31 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:01:31 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:01:31 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:01:33 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:01:33 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:01:44 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:01:44 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:01:44 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:01:45 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:01:45 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-completed
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro NO MATCH per status wc-completed
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato NO MATCH per status wc-completed
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 0
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29040, Status: wc-completed, Pulsanti trovati: 0
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-completed
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro NO MATCH per status wc-completed
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato NO MATCH per status wc-completed
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 0
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-completed, Pulsanti trovati: 0
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-completed
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro NO MATCH per status wc-completed
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato NO MATCH per status wc-completed
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 0
[14-Aug-2025 08:01:45 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29041, Status: wc-completed, Pulsanti trovati: 0
[14-Aug-2025 08:01:46 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:01:46 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:01:46 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:01:46 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:01:46 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:01:46 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:01:47 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 08:01:47 UTC] WAA: POST data: Array
(
)

[14-Aug-2025 08:01:47 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 08:01:47 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29040
                    [1] => post-29037
                    [2] => post-29041
                )

        )

    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 08:01:52 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:01:52 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:01:52 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:01:53 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:01:53 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:01:54 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:01:54 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:01:54 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:01:55 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:01:55 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-on-hold
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-on-hold
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-on-hold
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29040, Status: wc-on-hold, Pulsanti trovati: 2
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-on-hold
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-on-hold
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-on-hold
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-on-hold, Pulsanti trovati: 2
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-completed
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro NO MATCH per status wc-completed
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato NO MATCH per status wc-completed
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 0
[14-Aug-2025 08:01:55 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29041, Status: wc-completed, Pulsanti trovati: 0
[14-Aug-2025 08:01:56 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:01:56 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:01:56 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:01:57 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 08:01:57 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29040
                    [1] => post-29037
                    [2] => post-29041
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 08:01:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:01:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:01:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:02:00 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:02:00 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:02:04 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:02:04 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:02:04 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:02:04 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:02:04 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:02:04 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 08:02:04 UTC] WAA: POST data: Array
(
    [_wpnonce] => dad8c6440a
    [_wp_http_referer] => /wp-admin/post.php?post=29040&action=edit
    [user_ID] => 1
    [action] => editpost
    [originalaction] => editpost
    [post_author] => 1
    [post_type] => shop_order
    [original_post_status] => wc-on-hold
    [referredby] => http://test.test/wp-admin/edit.php?post_type=shop_order&paged=1&bulk_action=marked_on-hold&changed=2
    [_wp_original_http_referer] => http://test.test/wp-admin/edit.php?post_type=shop_order&paged=1&bulk_action=marked_on-hold&changed=2
    [post_ID] => 29040
    [meta-box-order-nonce] => 54b161f2d4
    [closedpostboxesnonce] => ca55cf2539
    [original_post_title] => Order &ndash; Agosto 14, 2025 @ 09:44 AM
    [post_title] => Ordine
    [samplepermalinknonce] => 788affc5c7
    [wc_order_action] => 
    [save] => Aggiorna
    [order_note] => 
    [order_note_type] => 
    [woocommerce_meta_nonce] => 22bf455e96
    [post_status] => on-hold
    [order_date] => 2025-08-14
    [order_date_hour] => 09
    [order_date_minute] => 44
    [order_date_second] => 33
    [order_status] => wc-pending
    [customer_user] => 1
    [_billing_first_name] => Giovanni
    [_billing_last_name] => Castaldo
    [_billing_company] => 
    [_billing_address_1] => via Giotto 13
    [_billing_address_2] => 
    [_billing_city] => Caserta
    [_billing_postcode] => 81100
    [_billing_country] => IT
    [_billing_state] => CE
    [_billing_email] => <EMAIL>
    [_billing_phone] => 
    [_payment_method] => bacs
    [_transaction_id] => 
    [_shipping_first_name] => Giovanni
    [_shipping_last_name] => Castaldo
    [_shipping_company] => 
    [_shipping_address_1] => via Giotto 13
    [_shipping_address_2] => 
    [_shipping_city] => Caserta
    [_shipping_postcode] => 81100
    [_shipping_country] => IT
    [_shipping_state] => CE
    [_shipping_phone] => 
    [customer_note] => 
    [order_item_id] => Array
        (
            [0] => 11
            [1] => 12
        )

    [order_item_tax_class] => Array
        (
            [11] => 
            [12] => 
        )

    [order_item_qty] => Array
        (
            [11] => 1
            [12] => 1
        )

    [refund_order_item_qty] => Array
        (
            [11] => 
            [12] => 
        )

    [line_subtotal] => Array
        (
            [11] => 199
            [12] => 429
        )

    [line_total] => Array
        (
            [11] => 199
            [12] => 429
        )

    [refund_line_total] => Array
        (
            [11] => 
            [12] => 
            [13] => 
        )

    [shipping_method_id] => Array
        (
            [0] => 13
        )

    [shipping_method_title] => Array
        (
            [13] => Spedizione gratuita
        )

    [shipping_method] => Array
        (
            [13] => free_shipping
        )

    [meta_key] => Array
        (
            [13] => Array
                (
                    [117] => Prodotti
                )

        )

    [meta_value] => Array
        (
            [13] => Array
                (
                    [117] => Augue adipiscing euismod &times; 1, Henectus tincidunt &times; 1
                )

        )

    [shipping_cost] => Array
        (
            [13] => 0
        )

    [restock_refunded_items] => on
    [refund_amount] => 
    [refund_reason] => 
    [refunded_amount] => 0
    [meta] => Array
        (
            [4271] => Array
                (
                    [key] => is_vat_exempt
                    [value] => no
                )

        )

    [_ajax_nonce] => 59214f2e5e
    [metakeyselect] => #NONE#
    [metakeyinput] => 
    [metavalue] => 
    [_ajax_nonce-add-meta] => 628a6549ec
)

[14-Aug-2025 08:02:05 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:02:05 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:02:05 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:02:06 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:02:06 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:02:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:02:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:02:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:02:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 08:02:07 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => dad8c6440a
    [post_ID] => 29040
    [active_post_lock] => 1755158520:1
)

[14-Aug-2025 08:02:08 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:02:08 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:02:08 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:02:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:02:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:02:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:02:17 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:02:17 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:02:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:02:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:02:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:02:22 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:02:22 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:02:22 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:02:23 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:02:23 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:02:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:02:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:02:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:02:27 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[14-Aug-2025 08:02:27 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-pending
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-pending
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-pending
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29040, Status: wc-pending, Pulsanti trovati: 2
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-completed
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro NO MATCH per status wc-completed
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato NO MATCH per status wc-completed
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 0
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-completed, Pulsanti trovati: 0
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-completed
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro NO MATCH per status wc-completed
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato NO MATCH per status wc-completed
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 0
[14-Aug-2025 08:02:27 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29041, Status: wc-completed, Pulsanti trovati: 0
[14-Aug-2025 08:02:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:02:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:02:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:02:28 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 08:02:28 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => dad8c6440a
    [post_ID] => 29040
    [active_post_lock] => 1755158526:1
)

[14-Aug-2025 08:02:29 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:02:29 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:02:29 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:02:30 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 08:02:30 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29040
                    [1] => post-29037
                    [2] => post-29041
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 08:02:38 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:02:38 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:02:38 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:02:38 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 08:02:38 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29040
                    [1] => post-29037
                    [2] => post-29041
                )

        )

    [interval] => 10
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[14-Aug-2025 08:03:24 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:03:24 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:03:24 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:03:25 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 08:03:25 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 08:03:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:03:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:03:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:03:26 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 08:03:26 UTC] WAA: POST data: Array
(
)

[14-Aug-2025 08:04:24 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:04:24 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:04:24 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:04:25 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 08:04:25 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 08:06:25 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:06:25 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:06:25 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:06:26 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 08:06:26 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 08:08:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:08:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:08:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:08:27 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 08:08:27 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

[14-Aug-2025 08:10:27 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[14-Aug-2025 08:10:27 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[14-Aug-2025 08:10:27 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[14-Aug-2025 08:10:28 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[14-Aug-2025 08:10:28 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 12e3ee74fe
    [action] => heartbeat
    [screen_id] => tools_page_wcos-cronjobs
    [has_focus] => false
)

