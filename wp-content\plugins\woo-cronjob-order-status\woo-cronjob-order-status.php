<?php
/**
 * Plugin Name: WooCommerce CronJob Order Status
 * Plugin URI: 
 * Description: Plugin per la gestione automatica degli stati ordine WooCommerce tramite cron job programmati con notifiche email ai clienti
 * Version: 1.0.0
 * Author: 
 * Text Domain: woo-cronjob-order-status
 * Domain Path: /languages
 * Requires at least: 5.8
 * Requires PHP: 8.0
 * WC requires at least: 6.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Verifica che WooCommerce sia attivo
if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins')))) {
    add_action('admin_notices', function() {
        echo '<div class="error"><p>' . 
             __('WooCommerce CronJob Order Status richiede WooCommerce per funzionare.', 'woo-cronjob-order-status') . 
             '</p></div>';
    });
    return;
}

// Definizione costanti
define('WCOS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WCOS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WCOS_VERSION', '1.0.0');
define('WCOS_TABLE_NAME', 'wcos_cronjobs');

// Caricamento file delle classi
require_once WCOS_PLUGIN_DIR . 'includes/class-woo-cronjob-db.php';
require_once WCOS_PLUGIN_DIR . 'includes/class-woo-cronjob-admin.php';
require_once WCOS_PLUGIN_DIR . 'includes/class-woo-cronjob-cron.php';
require_once WCOS_PLUGIN_DIR . 'includes/class-woo-cronjob-email.php';

// Inizializzazione del plugin
function wcos_init() {
    load_plugin_textdomain('woo-cronjob-order-status', false, dirname(plugin_basename(__FILE__)) . '/languages');
    
    if (is_admin()) {
        new Woo_Cronjob_Admin();
    }
    
    // Inizializza il sistema cron
    new Woo_Cronjob_Cron();
}
add_action('plugins_loaded', 'wcos_init');

// Attivazione del plugin
register_activation_hook(__FILE__, 'wcos_activate');
function wcos_activate() {
    $db = new Woo_Cronjob_DB();
    $db->create_tables();

    // Programma il cron principale se non esiste
    if (!wp_next_scheduled('wcos_check_cronjobs')) {
        wp_schedule_event(time(), 'hourly', 'wcos_check_cronjobs');
    }

    // Log dell'attivazione
    wcos_log('Plugin activated successfully');
}

// Hook per verificare e ricreare la tabella se necessario
add_action('admin_init', 'wcos_check_database');
function wcos_check_database() {
    // Solo per gli admin e solo nell'area admin
    if (!current_user_can('manage_options') || !is_admin()) {
        return;
    }

    // Controlla se la versione del database è aggiornata
    $db_version = get_option('wcos_version');
    if ($db_version !== WCOS_VERSION) {
        $db = new Woo_Cronjob_DB();
        $db->create_tables();
        wcos_log('Database updated to version ' . WCOS_VERSION);
    }
}

// Disattivazione del plugin
register_deactivation_hook(__FILE__, 'wcos_deactivate');
function wcos_deactivate() {
    // Rimuovi tutti i cron job programmati
    wp_clear_scheduled_hook('wcos_check_cronjobs');
    
    // Rimuovi i singoli cron job
    $db = new Woo_Cronjob_DB();
    $cronjobs = $db->get_all_cronjobs();
    
    foreach ($cronjobs as $cronjob) {
        wp_clear_scheduled_hook('wcos_execute_cronjob_' . $cronjob->slug_cronjob);
    }
}

// Disinstallazione del plugin
register_uninstall_hook(__FILE__, 'wcos_uninstall');
function wcos_uninstall() {
    global $wpdb;
    
    // Rimuovi la tabella del database
    $table_name = $wpdb->prefix . WCOS_TABLE_NAME;
    $wpdb->query("DROP TABLE IF EXISTS $table_name");
    
    // Rimuovi le opzioni del plugin
    delete_option('wcos_version');
}

// Funzioni di utilità
function wcos_get_woocommerce_order_statuses() {
    if (!function_exists('wc_get_order_statuses')) {
        return array();
    }
    
    return wc_get_order_statuses();
}

function wcos_get_cron_frequencies() {
    return array(
        'hourly' => __('Ogni ora', 'woo-cronjob-order-status'),
        'twicedaily' => __('Due volte al giorno', 'woo-cronjob-order-status'),
        'daily' => __('Giornaliero', 'woo-cronjob-order-status'),
        'weekly' => __('Settimanale', 'woo-cronjob-order-status'),
    );
}

function wcos_log($message, $level = 'info') {
    if (function_exists('wc_get_logger')) {
        $logger = wc_get_logger();
        $logger->log($level, $message, array('source' => 'woo-cronjob-order-status'));
    }
}

function wcos_sanitize_slug($slug) {
    return sanitize_title($slug);
}

function wcos_validate_email_template($template) {
    // Validazione base del template email
    if (empty($template)) {
        return false;
    }
    
    // Controlla che non contenga script dannosi
    if (strpos($template, '<script') !== false || strpos($template, 'javascript:') !== false) {
        return false;
    }
    
    return true;
}
