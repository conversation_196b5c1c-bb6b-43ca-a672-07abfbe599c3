<?php
/**
 * Test Action Buttons for WooCommerce CronJob Order Status Plugin
 * 
 * This file helps test the action button layout and responsiveness.
 * Access it via: /wp-content/plugins/woo-cronjob-order-status/test-action-buttons.php
 * 
 * IMPORTANT: Remove this file in production!
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('../../../wp-load.php');
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

// Check if our plugin is active
if (!defined('WCOS_VERSION')) {
    wp_die('WooCommerce CronJob Order Status plugin is not active');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>WCOS Action Buttons Test</title>
    <link rel="stylesheet" href="<?php echo admin_url('css/dashicons.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo WCOS_PLUGIN_URL; ?>assets/css/admin.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f1f1f1; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; background: white; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        
        .test-container {
            border: 2px dashed #ccc;
            padding: 15px;
            margin: 10px 0;
            background: #fafafa;
        }
        
        .width-test {
            border: 1px solid #999;
            background: #fff;
            overflow: hidden;
        }
        
        .width-200 { width: 200px; }
        .width-150 { width: 150px; }
        .width-100 { width: 100px; }
        .width-80 { width: 80px; }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        table th,
        table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        table th {
            background: #f9f9f9;
        }
        
        .column-actions {
            width: 22%;
            min-width: 200px;
        }
        
        .resize-handle {
            width: 100%;
            height: 10px;
            background: #0073aa;
            cursor: ew-resize;
            margin: 10px 0;
            position: relative;
        }
        
        .resize-handle::after {
            content: "← Trascina per ridimensionare →";
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>WooCommerce CronJob Order Status - Action Buttons Test</h1>
    
    <div class="test-section">
        <h2>Test Pulsanti Azione - Larghezza Normale</h2>
        <p>Questi sono i pulsanti con larghezza normale (200px+):</p>
        
        <div class="test-container width-test" style="width: 250px;">
            <div class="wcos-action-buttons">
                <a href="#" class="button button-edit">
                    <span class="dashicons dashicons-edit"></span>
                    <span class="button-text">Modifica</span>
                </a>
                <a href="#" class="button button-toggle">
                    <span class="dashicons dashicons-controls-pause"></span>
                    <span class="button-text">Disattiva</span>
                </a>
                <a href="#" class="button button-run">
                    <span class="dashicons dashicons-controls-play"></span>
                    <span class="button-text">Esegui</span>
                </a>
            </div>
        </div>
        
        <p class="success">✓ I pulsanti dovrebbero essere visibili con testo e icone</p>
    </div>
    
    <div class="test-section">
        <h2>Test Responsive - Larghezze Ridotte</h2>
        
        <h3>Larghezza 200px (Desktop piccolo)</h3>
        <div class="test-container width-test width-200">
            <div class="wcos-action-buttons">
                <a href="#" class="button button-edit">
                    <span class="dashicons dashicons-edit"></span>
                    <span class="button-text">Modifica</span>
                </a>
                <a href="#" class="button button-toggle">
                    <span class="dashicons dashicons-controls-pause"></span>
                    <span class="button-text">Disattiva</span>
                </a>
                <a href="#" class="button button-run">
                    <span class="dashicons dashicons-controls-play"></span>
                    <span class="button-text">Esegui</span>
                </a>
            </div>
        </div>
        
        <h3>Larghezza 150px (Tablet)</h3>
        <div class="test-container width-test width-150">
            <div class="wcos-action-buttons">
                <a href="#" class="button button-edit">
                    <span class="dashicons dashicons-edit"></span>
                    <span class="button-text">Modifica</span>
                </a>
                <a href="#" class="button button-toggle">
                    <span class="dashicons dashicons-controls-pause"></span>
                    <span class="button-text">Disattiva</span>
                </a>
                <a href="#" class="button button-run">
                    <span class="dashicons dashicons-controls-play"></span>
                    <span class="button-text">Esegui</span>
                </a>
            </div>
        </div>
        
        <h3>Larghezza 100px (Mobile)</h3>
        <div class="test-container width-test width-100">
            <div class="wcos-action-buttons">
                <a href="#" class="button button-edit">
                    <span class="dashicons dashicons-edit"></span>
                    <span class="button-text">Modifica</span>
                </a>
                <a href="#" class="button button-toggle">
                    <span class="dashicons dashicons-controls-pause"></span>
                    <span class="button-text">Disattiva</span>
                </a>
                <a href="#" class="button button-run">
                    <span class="dashicons dashicons-controls-play"></span>
                    <span class="button-text">Esegui</span>
                </a>
            </div>
        </div>
        
        <h3>Larghezza 80px (Mobile piccolo)</h3>
        <div class="test-container width-test width-80">
            <div class="wcos-action-buttons">
                <a href="#" class="button button-edit">
                    <span class="dashicons dashicons-edit"></span>
                    <span class="button-text">Modifica</span>
                </a>
                <a href="#" class="button button-toggle">
                    <span class="dashicons dashicons-controls-pause"></span>
                    <span class="button-text">Disattiva</span>
                </a>
                <a href="#" class="button button-run">
                    <span class="dashicons dashicons-controls-play"></span>
                    <span class="button-text">Esegui</span>
                </a>
            </div>
        </div>
        
        <p class="info">ℹ Su schermi piccoli il testo dovrebbe scomparire automaticamente</p>
    </div>
    
    <div class="test-section">
        <h2>Test Dashicons</h2>
        <p>Verifica che tutte le dashicons siano visibili:</p>
        
        <div style="display: flex; gap: 20px; align-items: center; margin: 20px 0;">
            <div>
                <strong>Edit:</strong>
                <span class="dashicons dashicons-edit" style="font-size: 20px;"></span>
            </div>
            <div>
                <strong>Pause:</strong>
                <span class="dashicons dashicons-controls-pause" style="font-size: 20px;"></span>
            </div>
            <div>
                <strong>Play:</strong>
                <span class="dashicons dashicons-controls-play" style="font-size: 20px;"></span>
            </div>
        </div>
        
        <p class="success">✓ Tutte le icone dovrebbero essere visibili</p>
    </div>
    
    <div class="test-section">
        <h2>Test in Tabella Simulata</h2>
        <p>Simulazione della tabella reale con colonne:</p>
        
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th style="width: 16%;">Nome</th>
                    <th style="width: 10%;">Slug</th>
                    <th style="width: 9%;">Frequenza</th>
                    <th style="width: 5%;">Giorni</th>
                    <th style="width: 11%;">Da Status</th>
                    <th style="width: 9%;">A Status</th>
                    <th style="width: 7%;">Stato</th>
                    <th style="width: 11%;">Prossima</th>
                    <th class="column-actions">Azioni</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Test CronJob Attivo</strong></td>
                    <td><code>test-cronjob</code></td>
                    <td>Giornaliero</td>
                    <td>7</td>
                    <td>In attesa</td>
                    <td>Elaborazione</td>
                    <td><span class="wcos-status wcos-status-active">Attivo</span></td>
                    <td>2024-01-15 10:00</td>
                    <td class="column-actions">
                        <div class="wcos-action-buttons">
                            <a href="#" class="button button-edit">
                                <span class="dashicons dashicons-edit"></span>
                                <span class="button-text">Modifica</span>
                            </a>
                            <a href="#" class="button button-toggle">
                                <span class="dashicons dashicons-controls-pause"></span>
                                <span class="button-text">Disattiva</span>
                            </a>
                            <a href="#" class="button button-run">
                                <span class="dashicons dashicons-controls-play"></span>
                                <span class="button-text">Esegui</span>
                            </a>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><strong>Test CronJob Inattivo</strong></td>
                    <td><code>test-cronjob-2</code></td>
                    <td>Settimanale</td>
                    <td>14</td>
                    <td>Completato</td>
                    <td>Archiviato</td>
                    <td><span class="wcos-status wcos-status-inactive">Inattivo</span></td>
                    <td>-</td>
                    <td class="column-actions">
                        <div class="wcos-action-buttons">
                            <a href="#" class="button button-edit">
                                <span class="dashicons dashicons-edit"></span>
                                <span class="button-text">Modifica</span>
                            </a>
                            <a href="#" class="button button-toggle inactive">
                                <span class="dashicons dashicons-controls-play"></span>
                                <span class="button-text">Attiva</span>
                            </a>
                            <a href="#" class="button button-run">
                                <span class="dashicons dashicons-controls-play"></span>
                                <span class="button-text">Esegui</span>
                            </a>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <p class="success">✓ I pulsanti dovrebbero stare nella colonna senza sforare</p>
    </div>
    
    <div class="test-section">
        <h2>Test Interattivo</h2>
        <p>Ridimensiona la finestra del browser per testare il comportamento responsive:</p>
        
        <div id="responsive-test" style="border: 2px solid #0073aa; padding: 15px; resize: horizontal; overflow: auto; min-width: 200px; max-width: 100%;">
            <div class="wcos-action-buttons">
                <a href="#" class="button button-edit">
                    <span class="dashicons dashicons-edit"></span>
                    <span class="button-text">Modifica</span>
                </a>
                <a href="#" class="button button-toggle">
                    <span class="dashicons dashicons-controls-pause"></span>
                    <span class="button-text">Disattiva</span>
                </a>
                <a href="#" class="button button-run">
                    <span class="dashicons dashicons-controls-play"></span>
                    <span class="button-text">Esegui</span>
                </a>
            </div>
        </div>
        
        <p class="info">ℹ Trascina l'angolo in basso a destra per ridimensionare</p>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
    jQuery(document).ready(function($) {
        // Test click events
        $('.button').on('click', function(e) {
            e.preventDefault();
            var buttonType = $(this).hasClass('button-edit') ? 'MODIFICA' : 
                           $(this).hasClass('button-toggle') ? 'TOGGLE' : 'ESEGUI';
            alert('Pulsante ' + buttonType + ' cliccato!');
        });
        
        // Monitor resize
        var resizeObserver = new ResizeObserver(function(entries) {
            for (let entry of entries) {
                var width = entry.contentRect.width;
                console.log('Container width:', width + 'px');
            }
        });
        
        var testElement = document.getElementById('responsive-test');
        if (testElement) {
            resizeObserver.observe(testElement);
        }
    });
    </script>
    
    <p><small><strong>Note:</strong> This test file should be removed in production environments for security reasons.</small></p>
</body>
</html>
