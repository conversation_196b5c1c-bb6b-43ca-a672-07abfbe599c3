# WooCommerce CronJob Order Status

Plugin WordPress per la gestione automatica degli stati ordine WooCommerce tramite cron job programmati con notifiche email ai clienti.

## Descrizione

Questo plugin permette agli amministratori di creare cron job automatici che:
1. Trovano ordini con stati specifici creati X giorni fa
2. Aggiornano questi ordini a un nuovo stato
3. Inviano notifiche email personalizzate ai clienti

## Caratteristiche

- **Interfaccia in Italiano**: Completamente localizzato in italiano
- **CRUD Completo**: Crea, modifica, elimina e gestisci cron job
- **Programmazione Flessibile**: Frequenze orarie, giornaliere, settimanali
- **Email Personalizzate**: Template email con placeholder dinamici
- **Integrazione WooCommerce**: Utilizza solo funzioni native di WordPress e WooCommerce
- **Sicurezza**: Validazione, sanitizzazione e controlli di sicurezza
- **Logging**: Sistema di log integrato per monitoraggio

## Requisiti

- WordPress 5.8+
- WooCommerce 6.0+
- PHP 8.0+

## Installazione

1. Carica la cartella del plugin in `/wp-content/plugins/`
2. Attiva il plugin dal pannello amministrativo WordPress
3. Vai su **Strumenti > Crea CronJob** per iniziare

## Utilizzo

### Creazione di un CronJob

1. Vai su **Strumenti > Crea CronJob**
2. Clicca su **Aggiungi Nuovo**
3. Compila i campi:
   - **Nome CronJob**: Nome descrittivo
   - **Slug CronJob**: Identificatore univoco (generato automaticamente)
   - **Cadenza Periodica**: Frequenza di esecuzione
   - **Numero Giorni**: Giorni dalla creazione ordine
   - **Status da Aggiornare**: Stati ordine da cercare
   - **Nuovo Status**: Stato a cui aggiornare
   - **Oggetto Email**: Oggetto dell'email al cliente
   - **Testo Email**: Contenuto dell'email con placeholder

### Placeholder Email Disponibili

- `{order_number}` - Numero ordine
- `{order_id}` - ID ordine
- `{order_date}` - Data ordine
- `{order_total}` - Totale ordine
- `{order_status}` - Stato ordine attuale
- `{customer_first_name}` - Nome cliente
- `{customer_last_name}` - Cognome cliente
- `{customer_full_name}` - Nome completo cliente
- `{customer_email}` - Email cliente
- `{billing_address}` - Indirizzo fatturazione
- `{shipping_address}` - Indirizzo spedizione
- `{site_name}` - Nome sito
- `{old_status}` - Stato precedente
- `{new_status}` - Nuovo stato
- `{order_items}` - Tabella articoli ordine
- `{order_view_url}` - URL visualizzazione ordine
- `{current_date}` - Data corrente
- `{current_time}` - Ora corrente

### Esempio di Utilizzo

**Scenario**: Aggiornare ordini "in attesa" dopo 14 giorni a "in elaborazione"

1. **Nome**: "Attivazione ordini in attesa"
2. **Cadenza**: Giornaliero
3. **Giorni**: 14
4. **Status da aggiornare**: "In attesa di pagamento"
5. **Nuovo status**: "In elaborazione"
6. **Oggetto email**: "Il tuo ordine #{order_number} è ora in elaborazione"
7. **Testo email**:
```
Ciao {customer_first_name},

Il tuo ordine #{order_number} del {order_date} è ora in elaborazione.

Dettagli ordine:
- Totale: {order_total}
- Stato precedente: {old_status}
- Nuovo stato: {new_status}

Puoi visualizzare il tuo ordine qui: {order_view_url}

Grazie per aver scelto {site_name}!
```

## Struttura File

```
woo-cronjob-order-status/
├── woo-cronjob-order-status.php    # File principale plugin
├── includes/
│   ├── class-woo-cronjob-admin.php # Gestione interfaccia admin
│   ├── class-woo-cronjob-db.php    # Operazioni database
│   ├── class-woo-cronjob-cron.php  # Esecuzione cron job
│   └── class-woo-cronjob-email.php # Gestione email
├── templates/
│   ├── admin-cronjob-list.php      # Lista cron job
│   └── admin-cronjob-form.php      # Form crea/modifica
├── assets/
│   ├── css/
│   │   └── admin.css               # Stili admin
│   └── js/
│       └── admin.js                # JavaScript admin
└── README.md                       # Documentazione
```

## Database

Il plugin crea una tabella `wp_wcos_cronjobs` con i seguenti campi:

- `id` - ID univoco
- `nome_cronjob` - Nome descrittivo
- `slug_cronjob` - Slug univoco
- `cadenza_periodica` - Frequenza esecuzione
- `numero_giorni` - Giorni dalla creazione ordine
- `status_da_aggiornare` - Stati da cercare (CSV)
- `nuovo_status` - Nuovo stato
- `oggetto_email` - Oggetto email
- `testo_email` - Contenuto email
- `attivo` - Stato attivo/inattivo
- `created_at` - Data creazione
- `updated_at` - Data ultimo aggiornamento

## Sicurezza

- Validazione e sanitizzazione di tutti i dati
- Controlli di permessi utente
- Nonce per prevenire CSRF
- Escape di output per prevenire XSS
- Validazione email template

## Logging

Il plugin utilizza il sistema di logging di WooCommerce per tracciare:
- Esecuzione cron job
- Ordini aggiornati
- Email inviate
- Errori e problemi

I log sono visibili in **WooCommerce > Stato > Log**.

## Troubleshooting

### I cron job non si eseguono

1. Verifica che il cron di WordPress sia configurato correttamente
2. Controlla che il cron job sia attivo
3. Verifica i log per errori
4. Assicurati che ci siano ordini che corrispondono ai criteri

### Le email non vengono inviate

1. Verifica la configurazione email di WordPress
2. Controlla che gli ordini abbiano email cliente valide
3. Verifica i log per errori di invio
4. Testa con un plugin SMTP se necessario

### Errori di permessi

1. Verifica che l'utente abbia il ruolo "Administrator"
2. Controlla i permessi file sul server
3. Verifica che WooCommerce sia attivo

## Supporto

Per supporto e segnalazione bug, contatta lo sviluppatore.

## Changelog

### 1.0.0
- Versione iniziale
- CRUD completo per cron job
- Sistema email con placeholder
- Interfaccia admin responsive
- Logging integrato
- Validazione e sicurezza

## Licenza

Questo plugin è rilasciato sotto licenza GPL v2 o successiva.
