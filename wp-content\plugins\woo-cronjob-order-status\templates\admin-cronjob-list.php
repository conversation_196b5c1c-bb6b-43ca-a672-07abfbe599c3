<?php
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php _e('Gestione CronJob Ordini', 'woo-cronjob-order-status'); ?></h1>
    <a href="<?php echo admin_url('tools.php?page=wcos-cronjobs&action=new'); ?>" class="page-title-action">
        <?php _e('Aggiungi Nuovo', 'woo-cronjob-order-status'); ?>
    </a>
    
    <hr class="wp-header-end">
    
    <?php if (empty($cronjobs)): ?>
        <div class="notice notice-info">
            <p><?php _e('Nessun cronjob configurato. Clicca su "Aggiungi Nuovo" per creare il primo cronjob.', 'woo-cronjob-order-status'); ?></p>
        </div>
    <?php else: ?>
        <div class="wcos-stats-boxes">
            <div class="wcos-stat-box">
                <h3><?php echo count($cronjobs); ?></h3>
                <p><?php _e('Totale CronJob', 'woo-cronjob-order-status'); ?></p>
            </div>
            <div class="wcos-stat-box">
                <h3><?php echo count(array_filter($cronjobs, function($c) { return $c->attivo; })); ?></h3>
                <p><?php _e('Attivi', 'woo-cronjob-order-status'); ?></p>
            </div>
            <div class="wcos-stat-box">
                <h3><?php echo count(array_filter($cronjobs, function($c) { return !$c->attivo; })); ?></h3>
                <p><?php _e('Inattivi', 'woo-cronjob-order-status'); ?></p>
            </div>
        </div>
        
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th scope="col" class="manage-column column-name column-primary">
                        <?php _e('Nome CronJob', 'woo-cronjob-order-status'); ?>
                    </th>
                    <th scope="col" class="manage-column column-slug">
                        <?php _e('Slug', 'woo-cronjob-order-status'); ?>
                    </th>
                    <th scope="col" class="manage-column column-frequency">
                        <?php _e('Frequenza', 'woo-cronjob-order-status'); ?>
                    </th>
                    <th scope="col" class="manage-column column-days">
                        <?php _e('Giorni', 'woo-cronjob-order-status'); ?>
                    </th>
                    <th scope="col" class="manage-column column-status-from">
                        <?php _e('Da Status', 'woo-cronjob-order-status'); ?>
                    </th>
                    <th scope="col" class="manage-column column-status-to">
                        <?php _e('A Status', 'woo-cronjob-order-status'); ?>
                    </th>
                    <th scope="col" class="manage-column column-active">
                        <?php _e('Stato', 'woo-cronjob-order-status'); ?>
                    </th>
                    <th scope="col" class="manage-column column-next-run">
                        <?php _e('Prossima Esecuzione', 'woo-cronjob-order-status'); ?>
                    </th>
                    <th scope="col" class="manage-column column-actions">
                        <?php _e('Azioni', 'woo-cronjob-order-status'); ?>
                    </th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($cronjobs as $cronjob): ?>
                    <tr>
                        <td class="column-name column-primary">
                            <strong>
                                <a href="<?php echo admin_url('tools.php?page=wcos-cronjobs&action=edit&id=' . $cronjob->id); ?>">
                                    <?php echo esc_html($cronjob->nome_cronjob); ?>
                                </a>
                            </strong>
                            <div class="row-actions">
                                <span class="edit">
                                    <a href="<?php echo admin_url('tools.php?page=wcos-cronjobs&action=edit&id=' . $cronjob->id); ?>">
                                        <?php _e('Modifica', 'woo-cronjob-order-status'); ?>
                                    </a> |
                                </span>
                                <span class="toggle">
                                    <a href="<?php echo wp_nonce_url(admin_url('admin-post.php?action=wcos_toggle_cronjob&id=' . $cronjob->id), 'wcos_toggle_cronjob', 'nonce'); ?>">
                                        <?php echo $cronjob->attivo ? __('Disattiva', 'woo-cronjob-order-status') : __('Attiva', 'woo-cronjob-order-status'); ?>
                                    </a> |
                                </span>
                                <span class="delete">
                                    <a href="<?php echo wp_nonce_url(admin_url('admin-post.php?action=wcos_delete_cronjob&id=' . $cronjob->id), 'wcos_delete_cronjob', 'nonce'); ?>" 
                                       onclick="return confirm('<?php _e('Sei sicuro di voler eliminare questo cronjob?', 'woo-cronjob-order-status'); ?>')">
                                        <?php _e('Elimina', 'woo-cronjob-order-status'); ?>
                                    </a>
                                </span>
                            </div>
                            <button type="button" class="toggle-row">
                                <span class="screen-reader-text"><?php _e('Mostra più dettagli', 'woo-cronjob-order-status'); ?></span>
                            </button>
                        </td>
                        <td class="column-slug" data-colname="<?php _e('Slug', 'woo-cronjob-order-status'); ?>">
                            <code><?php echo esc_html($cronjob->slug_cronjob); ?></code>
                        </td>
                        <td class="column-frequency" data-colname="<?php _e('Frequenza', 'woo-cronjob-order-status'); ?>">
                            <?php 
                            $frequencies = wcos_get_cron_frequencies();
                            echo isset($frequencies[$cronjob->cadenza_periodica]) ? 
                                 esc_html($frequencies[$cronjob->cadenza_periodica]) : 
                                 esc_html($cronjob->cadenza_periodica);
                            ?>
                        </td>
                        <td class="column-days" data-colname="<?php _e('Giorni', 'woo-cronjob-order-status'); ?>">
                            <?php
                            if (isset($cronjob->giorni_da) && isset($cronjob->giorni_a)) {
                                echo intval($cronjob->giorni_da) . '-' . intval($cronjob->giorni_a);
                            } else {
                                // Retrocompatibilità per vecchi record
                                echo isset($cronjob->numero_giorni) ? intval($cronjob->numero_giorni) : '0';
                            }
                            ?>
                        </td>
                        <td class="column-status-from" data-colname="<?php _e('Da Status', 'woo-cronjob-order-status'); ?>">
                            <?php 
                            $statuses_from = explode(',', $cronjob->status_da_aggiornare);
                            $order_statuses = wcos_get_woocommerce_order_statuses();
                            $status_names = array();
                            
                            foreach ($statuses_from as $status) {
                                $status = trim($status);
                                if (isset($order_statuses[$status])) {
                                    $status_names[] = $order_statuses[$status];
                                }
                            }
                            
                            echo esc_html(implode(', ', $status_names));
                            ?>
                        </td>
                        <td class="column-status-to" data-colname="<?php _e('A Status', 'woo-cronjob-order-status'); ?>">
                            <?php 
                            $order_statuses = wcos_get_woocommerce_order_statuses();
                            echo isset($order_statuses[$cronjob->nuovo_status]) ? 
                                 esc_html($order_statuses[$cronjob->nuovo_status]) : 
                                 esc_html($cronjob->nuovo_status);
                            ?>
                        </td>
                        <td class="column-active" data-colname="<?php _e('Stato', 'woo-cronjob-order-status'); ?>">
                            <?php if ($cronjob->attivo): ?>
                                <span class="wcos-status wcos-status-active">
                                    <span class="dashicons dashicons-yes-alt"></span>
                                    <?php _e('Attivo', 'woo-cronjob-order-status'); ?>
                                </span>
                            <?php else: ?>
                                <span class="wcos-status wcos-status-inactive">
                                    <span class="dashicons dashicons-dismiss"></span>
                                    <?php _e('Inattivo', 'woo-cronjob-order-status'); ?>
                                </span>
                            <?php endif; ?>
                        </td>
                        <td class="column-next-run" data-colname="<?php _e('Prossima Esecuzione', 'woo-cronjob-order-status'); ?>">
                            <?php 
                            if ($cronjob->attivo) {
                                $next_run = Woo_Cronjob_Cron::get_next_scheduled($cronjob->slug_cronjob);
                                if ($next_run) {
                                    echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $next_run);
                                } else {
                                    echo '<span class="wcos-warning">' . __('Non programmato', 'woo-cronjob-order-status') . '</span>';
                                }
                            } else {
                                echo '<span class="wcos-muted">' . __('Inattivo', 'woo-cronjob-order-status') . '</span>';
                            }
                            ?>
                        </td>
                        <td class="column-actions" data-colname="<?php _e('Azioni', 'woo-cronjob-order-status'); ?>">
                            <div class="wcos-action-buttons">
                                <a href="<?php echo admin_url('tools.php?page=wcos-cronjobs&action=edit&id=' . $cronjob->id); ?>"
                                   class="button button-edit" title="<?php _e('Modifica', 'woo-cronjob-order-status'); ?>">
                                    <span class="dashicons dashicons-edit"></span>
                                    <span class="button-text"><?php _e('Modifica', 'woo-cronjob-order-status'); ?></span>
                                </a>
                                <a href="<?php echo wp_nonce_url(admin_url('admin-post.php?action=wcos_toggle_cronjob&id=' . $cronjob->id), 'wcos_toggle_cronjob', 'nonce'); ?>"
                                   class="button button-toggle <?php echo $cronjob->attivo ? '' : 'inactive'; ?>"
                                   title="<?php echo $cronjob->attivo ? __('Disattiva', 'woo-cronjob-order-status') : __('Attiva', 'woo-cronjob-order-status'); ?>">
                                    <span class="dashicons dashicons-<?php echo $cronjob->attivo ? 'controls-pause' : 'controls-play'; ?>"></span>
                                    <span class="button-text"><?php echo $cronjob->attivo ? __('Disattiva', 'woo-cronjob-order-status') : __('Attiva', 'woo-cronjob-order-status'); ?></span>
                                </a>
                                <a href="<?php echo wp_nonce_url(admin_url('admin-post.php?action=wcos_run_cronjob&id=' . $cronjob->id), 'wcos_run_cronjob', 'nonce'); ?>"
                                   class="button button-run"
                                   title="<?php _e('Esegui Ora', 'woo-cronjob-order-status'); ?>"
                                   onclick="return confirm('<?php _e('Sei sicuro di voler eseguire questo cronjob ora?', 'woo-cronjob-order-status'); ?>')">
                                    <span class="dashicons dashicons-controls-play"></span>
                                    <span class="button-text"><?php _e('Esegui', 'woo-cronjob-order-status'); ?></span>
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <div class="wcos-info-box">
            <h3><?php _e('Informazioni sui CronJob', 'woo-cronjob-order-status'); ?></h3>
            <p><?php _e('I cronjob vengono eseguiti automaticamente secondo la frequenza impostata. Assicurati che il sistema cron di WordPress sia configurato correttamente sul tuo server.', 'woo-cronjob-order-status'); ?></p>
            <p><strong><?php _e('Nota:', 'woo-cronjob-order-status'); ?></strong> <?php _e('I cronjob inattivi non verranno eseguiti anche se programmati. Attiva un cronjob per iniziare l\'esecuzione automatica.', 'woo-cronjob-order-status'); ?></p>
        </div>
    <?php endif; ?>
</div>

<style>
.wcos-stats-boxes {
    display: flex;
    gap: 20px;
    margin: 20px 0;
}

.wcos-stat-box {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    min-width: 120px;
}

.wcos-stat-box h3 {
    font-size: 2em;
    margin: 0 0 5px 0;
    color: #0073aa;
}

.wcos-stat-box p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.wcos-status {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
}

.wcos-status-active {
    background-color: #d4edda;
    color: #155724;
}

.wcos-status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.wcos-warning {
    color: #856404;
    font-weight: 500;
}

.wcos-muted {
    color: #6c757d;
    font-style: italic;
}

.wcos-action-buttons {
    display: flex;
    gap: 5px;
}

.wcos-action-buttons .button {
    padding: 4px 8px;
    min-height: auto;
}

.wcos-action-buttons .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.wcos-info-box {
    background: #f0f6fc;
    border: 1px solid #c3dbf0;
    border-radius: 4px;
    padding: 15px;
    margin-top: 20px;
}

.wcos-info-box h3 {
    margin-top: 0;
    color: #0073aa;
}

.wcos-info-box p {
    margin-bottom: 10px;
}

.wcos-info-box p:last-child {
    margin-bottom: 0;
}

@media (max-width: 782px) {
    .wcos-stats-boxes {
        flex-direction: column;
    }
    
    .wcos-action-buttons {
        justify-content: center;
    }
}
</style>
