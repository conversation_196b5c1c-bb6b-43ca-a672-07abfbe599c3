<?php
if (!defined('ABSPATH')) {
    exit;
}

$form_title = $is_edit ? __('Modifica CronJob', 'woo-cronjob-order-status') : __('Nuovo CronJob', 'woo-cronjob-order-status');
$submit_text = $is_edit ? __('Aggiorna CronJob', 'woo-cronjob-order-status') : __('Crea CronJob', 'woo-cronjob-order-status');

// Valori di default
$nome_cronjob = $is_edit ? $cronjob->nome_cronjob : '';
$slug_cronjob = $is_edit ? $cronjob->slug_cronjob : '';
$cadenza_periodica = $is_edit ? $cronjob->cadenza_periodica : 'daily';
$giorni_da = $is_edit ? $cronjob->giorni_da : 7;
$giorni_a = $is_edit ? $cronjob->giorni_a : 14;
$status_da_aggiornare = $is_edit ? explode(',', $cronjob->status_da_aggiornare) : array();
$nuovo_status = $is_edit ? $cronjob->nuovo_status : '';
$oggetto_email = $is_edit ? $cronjob->oggetto_email : '';
$testo_email = $is_edit ? $cronjob->testo_email : '';
$attivo = $is_edit ? $cronjob->attivo : 1;
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php echo esc_html($form_title); ?></h1>
    <a href="<?php echo admin_url('tools.php?page=wcos-cronjobs'); ?>" class="page-title-action">
        <?php _e('← Torna alla Lista', 'woo-cronjob-order-status'); ?>
    </a>
    
    <hr class="wp-header-end">
    
    <form method="post" action="<?php echo admin_url('admin-post.php'); ?>" class="wcos-form">
        <input type="hidden" name="action" value="wcos_save_cronjob">
        <?php if ($is_edit): ?>
            <input type="hidden" name="cronjob_id" value="<?php echo $cronjob->id; ?>">
        <?php endif; ?>
        <?php wp_nonce_field('wcos_save_cronjob', 'wcos_nonce'); ?>
        
        <div class="wcos-form-container">
            <div class="wcos-form-main">
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php _e('Configurazione CronJob', 'woo-cronjob-order-status'); ?></h2>
                    </div>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="nome_cronjob"><?php _e('Nome CronJob', 'woo-cronjob-order-status'); ?> <span class="required">*</span></label>
                                </th>
                                <td>
                                    <input type="text" id="nome_cronjob" name="nome_cronjob" value="<?php echo esc_attr($nome_cronjob); ?>" 
                                           class="regular-text" required>
                                    <p class="description"><?php _e('Nome descrittivo per identificare il cronjob', 'woo-cronjob-order-status'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="slug_cronjob"><?php _e('Slug CronJob', 'woo-cronjob-order-status'); ?> <span class="required">*</span></label>
                                </th>
                                <td>
                                    <input type="text" id="slug_cronjob" name="slug_cronjob" value="<?php echo esc_attr($slug_cronjob); ?>" 
                                           class="regular-text" required pattern="[a-z0-9-]+" 
                                           title="<?php _e('Solo lettere minuscole, numeri e trattini', 'woo-cronjob-order-status'); ?>">
                                    <button type="button" id="generate_slug" class="button button-secondary">
                                        <?php _e('Genera Automaticamente', 'woo-cronjob-order-status'); ?>
                                    </button>
                                    <p class="description"><?php _e('Identificatore univoco (solo lettere minuscole, numeri e trattini)', 'woo-cronjob-order-status'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="cadenza_periodica"><?php _e('Cadenza Periodica', 'woo-cronjob-order-status'); ?> <span class="required">*</span></label>
                                </th>
                                <td>
                                    <select id="cadenza_periodica" name="cadenza_periodica" required>
                                        <?php foreach ($frequencies as $key => $label): ?>
                                            <option value="<?php echo esc_attr($key); ?>" <?php selected($cadenza_periodica, $key); ?>>
                                                <?php echo esc_html($label); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <p class="description"><?php _e('Frequenza di esecuzione del cronjob', 'woo-cronjob-order-status'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="giorni_da"><?php _e('Range Giorni', 'woo-cronjob-order-status'); ?> <span class="required">*</span></label>
                                </th>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px; flex-wrap: wrap;">
                                        <div>
                                            <label for="giorni_da" style="font-weight: normal;"><?php _e('Da', 'woo-cronjob-order-status'); ?>:</label>
                                            <input type="number" id="giorni_da" name="giorni_da" value="<?php echo esc_attr($giorni_da); ?>"
                                                   min="0" max="365" required style="width: 80px;">
                                            <span><?php _e('giorni fa', 'woo-cronjob-order-status'); ?></span>
                                        </div>
                                        <div>
                                            <label for="giorni_a" style="font-weight: normal;"><?php _e('A', 'woo-cronjob-order-status'); ?>:</label>
                                            <input type="number" id="giorni_a" name="giorni_a" value="<?php echo esc_attr($giorni_a); ?>"
                                                   min="1" max="365" required style="width: 80px;">
                                            <span><?php _e('giorni fa', 'woo-cronjob-order-status'); ?></span>
                                        </div>
                                    </div>
                                    <p class="description">
                                        <?php _e('Range di giorni dalla creazione dell\'ordine. Esempio: da 7 a 14 giorni fa significa ordini creati tra 14 e 7 giorni fa.', 'woo-cronjob-order-status'); ?>
                                    </p>
                                    <div id="range-preview" style="margin-top: 10px; padding: 10px; background: #f0f6fc; border-left: 4px solid #0073aa; display: none;">
                                        <strong><?php _e('Anteprima:', 'woo-cronjob-order-status'); ?></strong>
                                        <span id="range-text"></span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="status_da_aggiornare"><?php _e('Status Ordini da Aggiornare', 'woo-cronjob-order-status'); ?> <span class="required">*</span></label>
                                </th>
                                <td>
                                    <div class="wcos-status-checkboxes">
                                        <?php foreach ($order_statuses as $status_key => $status_label): ?>
                                            <label class="wcos-status-checkbox">
                                                <input type="checkbox" name="status_da_aggiornare[]" value="<?php echo esc_attr($status_key); ?>"
                                                       <?php echo in_array($status_key, $status_da_aggiornare) ? 'checked' : ''; ?>>
                                                <span class="wcos-status-label" data-status="<?php echo esc_attr($status_key); ?>">
                                                    <?php echo esc_html($status_label); ?>
                                                </span>
                                            </label>
                                        <?php endforeach; ?>
                                    </div>
                                    <p class="description">
                                        <?php _e('Seleziona uno o più stati ordine che devono essere aggiornati', 'woo-cronjob-order-status'); ?>
                                        <br>
                                        <a href="#" id="select-all-statuses"><?php _e('Seleziona tutti', 'woo-cronjob-order-status'); ?></a> |
                                        <a href="#" id="deselect-all-statuses"><?php _e('Deseleziona tutti', 'woo-cronjob-order-status'); ?></a>
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="nuovo_status"><?php _e('Nuovo Status Ordini', 'woo-cronjob-order-status'); ?> <span class="required">*</span></label>
                                </th>
                                <td>
                                    <select id="nuovo_status" name="nuovo_status" required>
                                        <option value=""><?php _e('Seleziona nuovo status', 'woo-cronjob-order-status'); ?></option>
                                        <?php foreach ($order_statuses as $status_key => $status_label): ?>
                                            <option value="<?php echo esc_attr($status_key); ?>" <?php selected($nuovo_status, $status_key); ?>>
                                                <?php echo esc_html($status_label); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <p class="description"><?php _e('Nuovo stato a cui verranno aggiornati gli ordini', 'woo-cronjob-order-status'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php _e('Configurazione Email', 'woo-cronjob-order-status'); ?></h2>
                    </div>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="oggetto_email"><?php _e('Oggetto Email', 'woo-cronjob-order-status'); ?> <span class="required">*</span></label>
                                </th>
                                <td>
                                    <input type="text" id="oggetto_email" name="oggetto_email" value="<?php echo esc_attr($oggetto_email); ?>" 
                                           class="large-text" required>
                                    <p class="description"><?php _e('Oggetto dell\'email che verrà inviata ai clienti', 'woo-cronjob-order-status'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="testo_email"><?php _e('Testo Email', 'woo-cronjob-order-status'); ?> <span class="required">*</span></label>
                                </th>
                                <td>
                                    <?php 
                                    wp_editor($testo_email, 'testo_email', array(
                                        'textarea_name' => 'testo_email',
                                        'textarea_rows' => 10,
                                        'media_buttons' => false,
                                        'teeny' => true,
                                        'quicktags' => true
                                    )); 
                                    ?>
                                    <p class="description"><?php _e('Contenuto dell\'email che verrà inviata ai clienti. Puoi utilizzare i placeholder elencati nella sidebar.', 'woo-cronjob-order-status'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="wcos-form-sidebar">
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php _e('Stato CronJob', 'woo-cronjob-order-status'); ?></h2>
                    </div>
                    <div class="inside">
                        <label>
                            <input type="checkbox" name="attivo" value="1" <?php checked($attivo, 1); ?>>
                            <?php _e('CronJob Attivo', 'woo-cronjob-order-status'); ?>
                        </label>
                        <p class="description"><?php _e('Se disattivato, il cronjob non verrà eseguito', 'woo-cronjob-order-status'); ?></p>
                    </div>
                </div>
                
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php _e('Placeholder Email', 'woo-cronjob-order-status'); ?></h2>
                    </div>
                    <div class="inside">
                        <p class="description"><?php _e('Clicca su un placeholder per inserirlo nel testo email:', 'woo-cronjob-order-status'); ?></p>
                        <div class="wcos-placeholders">
                            <?php 
                            $placeholders = Woo_Cronjob_Email::get_available_placeholders();
                            foreach ($placeholders as $placeholder => $description): 
                            ?>
                                <button type="button" class="wcos-placeholder-btn" data-placeholder="<?php echo esc_attr($placeholder); ?>" 
                                        title="<?php echo esc_attr($description); ?>">
                                    <?php echo esc_html($placeholder); ?>
                                </button>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php _e('Azioni', 'woo-cronjob-order-status'); ?></h2>
                    </div>
                    <div class="inside">
                        <div class="submitbox">
                            <div class="major-publishing-actions">
                                <div class="publishing-action">
                                    <input type="submit" name="submit" id="submit" class="button button-primary button-large" 
                                           value="<?php echo esc_attr($submit_text); ?>">
                                </div>
                                <div class="clear"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<style>
.wcos-form-container {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.wcos-form-main {
    flex: 1;
}

.wcos-form-sidebar {
    width: 300px;
}

.wcos-multiselect {
    width: 100%;
    height: 120px;
}

.wcos-status-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
    margin: 10px 0;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fafafa;
}

.wcos-status-checkbox {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.wcos-status-checkbox:hover {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.wcos-status-checkbox input[type="checkbox"] {
    margin-right: 8px;
    margin-top: 0;
}

.wcos-status-checkbox input[type="checkbox"]:checked + .wcos-status-label {
    font-weight: 600;
    color: #0073aa;
}

.wcos-status-label {
    font-size: 13px;
    line-height: 1.3;
}

#range-preview {
    font-size: 13px;
}

#select-all-statuses,
#deselect-all-statuses {
    text-decoration: none;
    color: #0073aa;
    font-size: 12px;
}

#select-all-statuses:hover,
#deselect-all-statuses:hover {
    text-decoration: underline;
}

.wcos-placeholders {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 12px;
}

.wcos-placeholder-btn {
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 6px 10px;
    font-size: 12px;
    font-family: 'Courier New', Courier, monospace;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #1d2327;
    font-weight: 500;
    line-height: 1.2;
    min-height: 28px;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
}

.wcos-placeholder-btn:hover {
    background: #0073aa;
    color: #ffffff;
    border-color: #0073aa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.wcos-placeholder-btn:active {
    transform: translateY(0);
}

.wcos-placeholder-btn:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

.required {
    color: #d63638;
}

@media (max-width: 1200px) {
    .wcos-form-container {
        flex-direction: column;
    }
    
    .wcos-form-sidebar {
        width: 100%;
    }
}

.submitbox {
    padding: 0;
}

.major-publishing-actions {
    border-top: 1px solid #dcdcde;
    padding: 10px;
    background: #f6f7f7;
}

.publishing-action {
    text-align: right;
}
</style>

<!-- JavaScript gestito da admin.js -->
