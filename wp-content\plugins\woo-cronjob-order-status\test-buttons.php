<?php
/**
 * Test Buttons for WooCommerce CronJob Order Status Plugin
 * 
 * This file helps test the button styles and functionality.
 * Access it via: /wp-content/plugins/woo-cronjob-order-status/test-buttons.php
 * 
 * IMPORTANT: Remove this file in production!
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('../../../wp-load.php');
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

// Check if our plugin is active
if (!defined('WCOS_VERSION')) {
    wp_die('WooCommerce CronJob Order Status plugin is not active');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>WCOS Button Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f1f1f1; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; background: white; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        
        /* Import our button styles */
        .wcos-action-buttons {
            display: flex;
            gap: 4px;
            justify-content: flex-start;
            align-items: center;
            margin: 10px 0;
        }

        .wcos-action-buttons .button {
            padding: 6px 10px;
            min-height: 32px;
            line-height: 1.2;
            border-radius: 3px;
            font-size: 13px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            border: 1px solid #c3c4c7;
            background: #f6f7f7;
            color: #2c3338;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .wcos-action-buttons .button:hover {
            background: #f0f0f1;
            border-color: #8c8f94;
            color: #1d2327;
        }

        .wcos-action-buttons .button.button-edit {
            background: #f0f6fc;
            border-color: #c3dbf0;
            color: #0073aa;
        }

        .wcos-action-buttons .button.button-edit:hover {
            background: #0073aa;
            border-color: #0073aa;
            color: #ffffff;
        }

        .wcos-action-buttons .button.button-toggle {
            background: #fff8e1;
            border-color: #ffcc02;
            color: #8a6914;
        }

        .wcos-action-buttons .button.button-toggle:hover {
            background: #ffcc02;
            border-color: #ffcc02;
            color: #1d2327;
        }

        .wcos-action-buttons .button.button-run {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }

        .wcos-action-buttons .button.button-run:hover {
            background: #4caf50;
            border-color: #4caf50;
            color: #ffffff;
        }

        .dashicons {
            font-family: dashicons;
            font-size: 16px;
            width: 16px;
            height: 16px;
            line-height: 16px;
            margin-right: 4px;
            text-decoration: inherit;
            font-weight: normal;
            font-style: normal;
            vertical-align: top;
        }

        .dashicons-edit:before { content: "\f464"; }
        .dashicons-pause:before { content: "\f523"; }
        .dashicons-controls-play:before { content: "\f522"; }
        
        .test-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .test-table th,
        .test-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .test-table th {
            background: #f9f9f9;
            font-weight: bold;
        }
        
        .old-buttons {
            opacity: 0.5;
        }
        
        .old-buttons .button {
            padding: 4px 8px;
            min-height: auto;
            line-height: 1;
            background: #f6f7f7;
            border: 1px solid #c3c4c7;
            color: #2c3338;
        }
    </style>
    <link rel="stylesheet" href="<?php echo admin_url('css/dashicons.min.css'); ?>">
</head>
<body>
    <h1>WooCommerce CronJob Order Status - Button Test</h1>
    
    <div class="test-section">
        <h2>Nuovi Pulsanti Azione (Migliorati)</h2>
        <p>Questi sono i nuovi pulsanti con stili migliorati:</p>
        
        <div class="wcos-action-buttons">
            <a href="#" class="button button-edit" onclick="alert('Modifica clicked!'); return false;">
                <span class="dashicons dashicons-edit"></span>
                Modifica
            </a>
            <a href="#" class="button button-toggle" onclick="alert('Toggle clicked!'); return false;">
                <span class="dashicons dashicons-pause"></span>
                Disattiva
            </a>
            <a href="#" class="button button-run" onclick="alert('Esegui clicked!'); return false;">
                <span class="dashicons dashicons-controls-play"></span>
                Esegui
            </a>
        </div>
        
        <p class="success">✓ I pulsanti dovrebbero essere chiaramente visibili con colori distintivi</p>
        <p class="info">• Blu per Modifica • Giallo per Toggle • Verde per Esegui</p>
    </div>
    
    <div class="test-section">
        <h2>Confronto: Prima vs Dopo</h2>
        
        <table class="test-table">
            <thead>
                <tr>
                    <th>Versione</th>
                    <th>Pulsanti</th>
                    <th>Visibilità</th>
                    <th>Note</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Prima (Vecchi)</strong></td>
                    <td>
                        <div class="wcos-action-buttons old-buttons">
                            <a href="#" class="button button-small">
                                <span class="dashicons dashicons-edit"></span>
                            </a>
                            <a href="#" class="button button-small">
                                <span class="dashicons dashicons-pause"></span>
                            </a>
                        </div>
                    </td>
                    <td><span class="error">Scarsa</span></td>
                    <td>Solo icone, difficili da distinguere</td>
                </tr>
                <tr>
                    <td><strong>Dopo (Nuovi)</strong></td>
                    <td>
                        <div class="wcos-action-buttons">
                            <a href="#" class="button button-edit">
                                <span class="dashicons dashicons-edit"></span>
                                Modifica
                            </a>
                            <a href="#" class="button button-toggle">
                                <span class="dashicons dashicons-pause"></span>
                                Disattiva
                            </a>
                            <a href="#" class="button button-run">
                                <span class="dashicons dashicons-controls-play"></span>
                                Esegui
                            </a>
                        </div>
                    </td>
                    <td><span class="success">Ottima</span></td>
                    <td>Testo + icone, colori distintivi</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="test-section">
        <h2>Test Responsive</h2>
        <p>Riduci la finestra del browser per testare il comportamento responsive:</p>
        
        <div style="max-width: 400px; border: 2px dashed #ccc; padding: 10px;">
            <p><strong>Simulazione Mobile:</strong></p>
            <div class="wcos-action-buttons" style="flex-wrap: wrap; gap: 3px;">
                <a href="#" class="button button-edit" style="font-size: 11px; padding: 4px 6px;">
                    <span class="dashicons dashicons-edit" style="font-size: 14px; margin-right: 2px;"></span>
                    Modifica
                </a>
                <a href="#" class="button button-toggle" style="font-size: 11px; padding: 4px 6px;">
                    <span class="dashicons dashicons-pause" style="font-size: 14px; margin-right: 2px;"></span>
                    Disattiva
                </a>
                <a href="#" class="button button-run" style="font-size: 11px; padding: 4px 6px;">
                    <span class="dashicons dashicons-controls-play" style="font-size: 14px; margin-right: 2px;"></span>
                    Esegui
                </a>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Test Funzionalità</h2>
        <p>Clicca sui pulsanti per testare gli eventi JavaScript:</p>
        
        <div class="wcos-action-buttons">
            <a href="#" class="button button-edit" id="test-edit">
                <span class="dashicons dashicons-edit"></span>
                Test Modifica
            </a>
            <a href="#" class="button button-toggle" id="test-toggle">
                <span class="dashicons dashicons-pause"></span>
                Test Toggle
            </a>
            <a href="#" class="button button-run" id="test-run">
                <span class="dashicons dashicons-controls-play"></span>
                Test Esegui
            </a>
        </div>
        
        <div id="test-log" style="background: #f0f0f0; padding: 10px; margin-top: 10px; min-height: 50px;">
            <strong>Log test:</strong><br>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
    jQuery(document).ready(function($) {
        var testCount = 0;
        
        $('#test-edit').on('click', function(e) {
            e.preventDefault();
            testCount++;
            $('#test-log').append('<div>' + testCount + '. Pulsante MODIFICA cliccato</div>');
        });
        
        $('#test-toggle').on('click', function(e) {
            e.preventDefault();
            testCount++;
            $('#test-log').append('<div>' + testCount + '. Pulsante TOGGLE cliccato</div>');
        });
        
        $('#test-run').on('click', function(e) {
            e.preventDefault();
            testCount++;
            $('#test-log').append('<div>' + testCount + '. Pulsante ESEGUI cliccato</div>');
        });
    });
    </script>
    
    <p><small><strong>Note:</strong> This test file should be removed in production environments for security reasons.</small></p>
</body>
</html>
