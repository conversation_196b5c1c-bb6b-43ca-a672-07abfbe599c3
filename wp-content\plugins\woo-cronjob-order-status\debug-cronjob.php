<?php
/**
 * Debug CronJob Execution for WooCommerce CronJob Order Status Plugin
 * 
 * This file helps debug cronjob execution issues.
 * Access it via: /wp-content/plugins/woo-cronjob-order-status/debug-cronjob.php
 * 
 * IMPORTANT: Remove this file in production!
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('../../../wp-load.php');
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

// Check if our plugin is active
if (!defined('WCOS_VERSION')) {
    wp_die('WooCommerce CronJob Order Status plugin is not active');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>WCOS CronJob Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .debug-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .test-button { background: #0073aa; color: white; padding: 10px 20px; border: none; cursor: pointer; margin: 10px 0; }
        .test-button:hover { background: #005a87; }
        .log-entry { margin: 5px 0; padding: 5px; border-left: 3px solid #ccc; }
        .log-error { border-left-color: #d63638; background: #fef7f7; }
        .log-warning { border-left-color: #dba617; background: #fffbf0; }
        .log-success { border-left-color: #00a32a; background: #f0f6fc; }
    </style>
</head>
<body>
    <h1>WooCommerce CronJob Order Status - CronJob Debug</h1>
    
    <div class="debug-section">
        <h2>CronJob List</h2>
        <?php
        $db = new Woo_Cronjob_DB();
        $cronjobs = $db->get_all_cronjobs();
        
        if (empty($cronjobs)) {
            echo '<p class="warning">⚠ Nessun cronjob configurato</p>';
        } else {
            echo '<table border="1" cellpadding="5" style="width: 100%; border-collapse: collapse;">';
            echo '<tr><th>ID</th><th>Nome</th><th>Slug</th><th>Attivo</th><th>Frequenza</th><th>Giorni</th><th>Status Da</th><th>Status A</th><th>Azioni</th></tr>';
            
            foreach ($cronjobs as $cronjob) {
                $status_class = $cronjob->attivo ? 'success' : 'error';
                $status_text = $cronjob->attivo ? 'Attivo' : 'Inattivo';
                
                echo '<tr>';
                echo '<td>' . $cronjob->id . '</td>';
                echo '<td>' . esc_html($cronjob->nome_cronjob) . '</td>';
                echo '<td><code>' . esc_html($cronjob->slug_cronjob) . '</code></td>';
                echo '<td class="' . $status_class . '">' . $status_text . '</td>';
                echo '<td>' . esc_html($cronjob->cadenza_periodica) . '</td>';
                echo '<td>' . $cronjob->numero_giorni . '</td>';
                echo '<td>' . esc_html($cronjob->status_da_aggiornare) . '</td>';
                echo '<td>' . esc_html($cronjob->nuovo_status) . '</td>';
                echo '<td>';
                echo '<form method="post" style="display: inline;">';
                echo '<input type="hidden" name="action" value="test_cronjob">';
                echo '<input type="hidden" name="cronjob_id" value="' . $cronjob->id . '">';
                echo '<button type="submit" class="test-button" style="padding: 5px 10px; font-size: 12px;">Test</button>';
                echo '</form>';
                echo '</td>';
                echo '</tr>';
            }
            
            echo '</table>';
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2>Order Status Test</h2>
        <?php
        $order_statuses = wcos_get_woocommerce_order_statuses();
        if (!empty($order_statuses)) {
            echo '<p class="success">✓ WooCommerce order statuses disponibili:</p>';
            echo '<ul>';
            foreach ($order_statuses as $key => $label) {
                echo '<li><code>' . esc_html($key) . '</code> - ' . esc_html($label) . '</li>';
            }
            echo '</ul>';
        } else {
            echo '<p class="error">✗ Nessun order status trovato</p>';
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2>Sample Orders</h2>
        <?php
        $orders = wc_get_orders(array(
            'limit' => 10,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        if (!empty($orders)) {
            echo '<p class="success">✓ Ordini di esempio trovati:</p>';
            echo '<table border="1" cellpadding="5" style="width: 100%; border-collapse: collapse;">';
            echo '<tr><th>ID</th><th>Status</th><th>Data Creazione</th><th>Cliente</th><th>Totale</th></tr>';
            
            foreach ($orders as $order) {
                echo '<tr>';
                echo '<td>#' . $order->get_id() . '</td>';
                echo '<td><code>' . $order->get_status() . '</code></td>';
                echo '<td>' . $order->get_date_created()->date('Y-m-d H:i:s') . '</td>';
                echo '<td>' . esc_html($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()) . '</td>';
                echo '<td>' . $order->get_formatted_order_total() . '</td>';
                echo '</tr>';
            }
            
            echo '</table>';
        } else {
            echo '<p class="warning">⚠ Nessun ordine trovato</p>';
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2>Email Placeholder Test</h2>
        <?php
        try {
            $email = new Woo_Cronjob_Email();
            $placeholders = Woo_Cronjob_Email::get_available_placeholders();
            
            echo '<p class="success">✓ Placeholder email caricati correttamente:</p>';
            echo '<p><strong>Totale placeholder:</strong> ' . count($placeholders) . '</p>';
            
            // Test con ordine fittizio
            if (!empty($orders)) {
                $test_order = $orders[0];
                echo '<h3>Test con ordine reale #' . $test_order->get_id() . ':</h3>';
                
                $test_subject = "Test: {customer_first_name}, il tuo ordine {order_number} è stato aggiornato";
                $test_message = "Ciao {customer_first_name},\n\nIl tuo ordine #{order_number} del {order_date} è stato aggiornato da {old_status} a {new_status}.\n\nTotale: {order_total}\n\nGrazie!";
                
                // Simula la sostituzione dei placeholder
                $email_instance = new Woo_Cronjob_Email();
                $reflection = new ReflectionClass($email_instance);
                $method = $reflection->getMethod('replace_placeholders');
                $method->setAccessible(true);
                
                $processed_subject = $method->invoke($email_instance, $test_subject, $test_order, 'pending', 'processing');
                $processed_message = $method->invoke($email_instance, $test_message, $test_order, 'pending', 'processing');
                
                echo '<h4>Oggetto originale:</h4>';
                echo '<pre>' . esc_html($test_subject) . '</pre>';
                echo '<h4>Oggetto processato:</h4>';
                echo '<pre>' . esc_html($processed_subject) . '</pre>';
                
                echo '<h4>Messaggio originale:</h4>';
                echo '<pre>' . esc_html($test_message) . '</pre>';
                echo '<h4>Messaggio processato:</h4>';
                echo '<pre>' . esc_html($processed_message) . '</pre>';
            }
            
        } catch (Exception $e) {
            echo '<p class="error">✗ Errore nel test placeholder: ' . esc_html($e->getMessage()) . '</p>';
        }
        ?>
    </div>
    
    <?php
    // Handle form submissions
    if ($_POST['action'] ?? '') {
        echo '<div class="debug-section">';
        echo '<h2>Risultato Test</h2>';
        
        if ($_POST['action'] === 'test_cronjob') {
            $cronjob_id = intval($_POST['cronjob_id']);
            echo '<p>Esecuzione test cronjob ID: ' . $cronjob_id . '</p>';
            
            try {
                $cron = new Woo_Cronjob_Cron();
                $result = $cron->manual_execute($cronjob_id);
                
                if (is_wp_error($result)) {
                    echo '<p class="error">✗ Errore: ' . esc_html($result->get_error_message()) . '</p>';
                } else {
                    echo '<p class="success">✓ CronJob eseguito con successo!</p>';
                }
                
            } catch (Exception $e) {
                echo '<p class="error">✗ Eccezione: ' . esc_html($e->getMessage()) . '</p>';
                echo '<pre>Stack trace: ' . esc_html($e->getTraceAsString()) . '</pre>';
            }
        }
        
        echo '</div>';
    }
    ?>
    
    <div class="debug-section">
        <h2>WordPress Cron Status</h2>
        <?php
        if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) {
            echo '<p class="warning">⚠ WordPress Cron è DISABILITATO (DISABLE_WP_CRON = true)</p>';
        } else {
            echo '<p class="success">✓ WordPress Cron è abilitato</p>';
        }
        
        // Check scheduled crons
        $crons = _get_cron_array();
        $wcos_crons = array();
        
        foreach ($crons as $timestamp => $cron) {
            foreach ($cron as $hook => $events) {
                if (strpos($hook, 'wcos_') === 0) {
                    $wcos_crons[] = array(
                        'hook' => $hook,
                        'timestamp' => $timestamp,
                        'next_run' => date('Y-m-d H:i:s', $timestamp)
                    );
                }
            }
        }
        
        if (!empty($wcos_crons)) {
            echo '<p class="success">✓ Cron jobs WCOS programmati: ' . count($wcos_crons) . '</p>';
            echo '<ul>';
            foreach ($wcos_crons as $cron) {
                echo '<li>' . esc_html($cron['hook']) . ' - Prossima esecuzione: ' . esc_html($cron['next_run']) . '</li>';
            }
            echo '</ul>';
        } else {
            echo '<p class="info">ℹ Nessun cron job WCOS programmato</p>';
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2>Recent Logs</h2>
        <p>Controlla i log di WooCommerce per dettagli sull'esecuzione:</p>
        <p><a href="<?php echo admin_url('admin.php?page=wc-status&tab=logs'); ?>" target="_blank">Vai ai Log di WooCommerce</a></p>
    </div>
    
    <p><small><strong>Note:</strong> This debug file should be removed in production environments for security reasons.</small></p>
</body>
</html>
