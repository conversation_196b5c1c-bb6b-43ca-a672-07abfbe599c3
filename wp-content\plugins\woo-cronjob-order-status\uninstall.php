<?php
/**
 * Uninstall script for WooCommerce CronJob Order Status
 * 
 * This file is executed when the plugin is deleted from WordPress admin.
 * It cleans up all plugin data including database tables, options, and scheduled crons.
 */

// If uninstall not called from WordPress, then exit
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Define constants if not already defined
if (!defined('WCOS_TABLE_NAME')) {
    define('WCOS_TABLE_NAME', 'wcos_cronjobs');
}

/**
 * Remove all scheduled cron jobs
 */
function wcos_cleanup_cron_jobs() {
    global $wpdb;
    
    // Get all cronjobs from database before deleting the table
    $table_name = $wpdb->prefix . WCOS_TABLE_NAME;
    $cronjobs = $wpdb->get_results("SELECT slug_cronjob FROM {$table_name}");
    
    // Remove individual cron jobs
    if ($cronjobs) {
        foreach ($cronjobs as $cronjob) {
            $hook = 'wcos_execute_cronjob_' . $cronjob->slug_cronjob;
            wp_clear_scheduled_hook($hook);
        }
    }
    
    // Remove main cron job
    wp_clear_scheduled_hook('wcos_check_cronjobs');
    
    // Clean up any orphaned cron jobs
    $crons = _get_cron_array();
    if ($crons) {
        foreach ($crons as $timestamp => $cron) {
            foreach ($cron as $hook => $events) {
                if (strpos($hook, 'wcos_') === 0) {
                    wp_clear_scheduled_hook($hook);
                }
            }
        }
    }
}

/**
 * Remove database table
 */
function wcos_cleanup_database() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . WCOS_TABLE_NAME;
    $wpdb->query("DROP TABLE IF EXISTS {$table_name}");
}

/**
 * Remove plugin options
 */
function wcos_cleanup_options() {
    // Remove plugin version option
    delete_option('wcos_version');
    
    // Remove any other plugin options that might exist
    delete_option('wcos_settings');
    delete_option('wcos_last_cleanup');
    
    // Clean up transients
    delete_transient('wcos_stats');
    delete_transient('wcos_cron_status');
}

/**
 * Remove user meta related to plugin
 */
function wcos_cleanup_user_meta() {
    global $wpdb;
    
    // Remove any user meta keys related to the plugin
    $wpdb->query("DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE 'wcos_%'");
}

/**
 * Clean up log files
 */
function wcos_cleanup_logs() {
    // WooCommerce logs are automatically cleaned up by WooCommerce
    // But we can remove any custom log files if they exist
    
    $upload_dir = wp_upload_dir();
    $log_dir = $upload_dir['basedir'] . '/wcos-logs/';
    
    if (is_dir($log_dir)) {
        $files = glob($log_dir . '*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        rmdir($log_dir);
    }
}

/**
 * Main cleanup function
 */
function wcos_uninstall_cleanup() {
    // Only proceed if user has proper permissions
    if (!current_user_can('activate_plugins')) {
        return;
    }
    
    // Check if this is a multisite installation
    if (is_multisite()) {
        // Get all blog IDs
        $blog_ids = get_sites(array('fields' => 'ids'));
        
        foreach ($blog_ids as $blog_id) {
            switch_to_blog($blog_id);
            
            // Perform cleanup for this site
            wcos_cleanup_cron_jobs();
            wcos_cleanup_database();
            wcos_cleanup_options();
            wcos_cleanup_user_meta();
            wcos_cleanup_logs();
            
            restore_current_blog();
        }
    } else {
        // Single site cleanup
        wcos_cleanup_cron_jobs();
        wcos_cleanup_database();
        wcos_cleanup_options();
        wcos_cleanup_user_meta();
        wcos_cleanup_logs();
    }
}

// Execute cleanup
wcos_uninstall_cleanup();

// Log the uninstall (if WooCommerce is still active)
if (function_exists('wc_get_logger')) {
    $logger = wc_get_logger();
    $logger->info('WooCommerce CronJob Order Status plugin uninstalled and cleaned up', array('source' => 'woo-cronjob-order-status'));
}
