<?php
if (!defined('ABSPATH')) {
    exit;
}

class Woo_Cronjob_DB {
    private $table_name;
    
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . WCOS_TABLE_NAME;
    }
    
    /**
     * <PERSON><PERSON> le tabelle del database
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE {$this->table_name} (
            id int(11) NOT NULL AUTO_INCREMENT,
            nome_cronjob varchar(255) NOT NULL,
            slug_cronjob varchar(255) NOT NULL UNIQUE,
            cadenza_periodica varchar(50) NOT NULL,
            numero_giorni int(11) NOT NULL,
            status_da_aggiornare text NOT NULL,
            nuovo_status varchar(50) NOT NULL,
            oggetto_email varchar(500) NOT NULL,
            testo_email text NOT NULL,
            attivo tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY slug_cronjob (slug_cronjob),
            KEY attivo (attivo)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Salva la versione del database
        update_option('wcos_version', WCOS_VERSION);
    }
    
    /**
     * Inserisce un nuovo cronjob
     */
    public function insert_cronjob($data) {
        global $wpdb;
        
        $data = $this->sanitize_cronjob_data($data);
        
        $result = $wpdb->insert(
            $this->table_name,
            array(
                'nome_cronjob' => $data['nome_cronjob'],
                'slug_cronjob' => $data['slug_cronjob'],
                'cadenza_periodica' => $data['cadenza_periodica'],
                'numero_giorni' => $data['numero_giorni'],
                'status_da_aggiornare' => $data['status_da_aggiornare'],
                'nuovo_status' => $data['nuovo_status'],
                'oggetto_email' => $data['oggetto_email'],
                'testo_email' => $data['testo_email'],
                'attivo' => $data['attivo']
            ),
            array('%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%d')
        );
        
        if ($result === false) {
            return new WP_Error('db_error', __('Errore durante l\'inserimento del cronjob', 'woo-cronjob-order-status'));
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * Aggiorna un cronjob esistente
     */
    public function update_cronjob($id, $data) {
        global $wpdb;
        
        $data = $this->sanitize_cronjob_data($data);
        
        $result = $wpdb->update(
            $this->table_name,
            array(
                'nome_cronjob' => $data['nome_cronjob'],
                'slug_cronjob' => $data['slug_cronjob'],
                'cadenza_periodica' => $data['cadenza_periodica'],
                'numero_giorni' => $data['numero_giorni'],
                'status_da_aggiornare' => $data['status_da_aggiornare'],
                'nuovo_status' => $data['nuovo_status'],
                'oggetto_email' => $data['oggetto_email'],
                'testo_email' => $data['testo_email'],
                'attivo' => $data['attivo']
            ),
            array('id' => $id),
            array('%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%d'),
            array('%d')
        );
        
        if ($result === false) {
            return new WP_Error('db_error', __('Errore durante l\'aggiornamento del cronjob', 'woo-cronjob-order-status'));
        }
        
        return true;
    }
    
    /**
     * Elimina un cronjob
     */
    public function delete_cronjob($id) {
        global $wpdb;
        
        $result = $wpdb->delete(
            $this->table_name,
            array('id' => $id),
            array('%d')
        );
        
        if ($result === false) {
            return new WP_Error('db_error', __('Errore durante l\'eliminazione del cronjob', 'woo-cronjob-order-status'));
        }
        
        return true;
    }
    
    /**
     * Ottiene un cronjob per ID
     */
    public function get_cronjob($id) {
        global $wpdb;
        
        return $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$this->table_name} WHERE id = %d", $id)
        );
    }
    
    /**
     * Ottiene un cronjob per slug
     */
    public function get_cronjob_by_slug($slug) {
        global $wpdb;
        
        return $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$this->table_name} WHERE slug_cronjob = %s", $slug)
        );
    }
    
    /**
     * Ottiene tutti i cronjob
     */
    public function get_all_cronjobs($active_only = false) {
        global $wpdb;
        
        $where = $active_only ? "WHERE attivo = 1" : "";
        
        return $wpdb->get_results("SELECT * FROM {$this->table_name} {$where} ORDER BY created_at DESC");
    }
    
    /**
     * Conta i cronjob
     */
    public function count_cronjobs($active_only = false) {
        global $wpdb;
        
        $where = $active_only ? "WHERE attivo = 1" : "";
        
        return $wpdb->get_var("SELECT COUNT(*) FROM {$this->table_name} {$where}");
    }
    
    /**
     * Verifica se uno slug esiste già
     */
    public function slug_exists($slug, $exclude_id = null) {
        global $wpdb;
        
        $sql = "SELECT COUNT(*) FROM {$this->table_name} WHERE slug_cronjob = %s";
        $params = array($slug);
        
        if ($exclude_id) {
            $sql .= " AND id != %d";
            $params[] = $exclude_id;
        }
        
        return $wpdb->get_var($wpdb->prepare($sql, $params)) > 0;
    }
    
    /**
     * Attiva/disattiva un cronjob
     */
    public function toggle_cronjob_status($id) {
        global $wpdb;
        
        $current_status = $wpdb->get_var(
            $wpdb->prepare("SELECT attivo FROM {$this->table_name} WHERE id = %d", $id)
        );
        
        $new_status = $current_status ? 0 : 1;
        
        return $wpdb->update(
            $this->table_name,
            array('attivo' => $new_status),
            array('id' => $id),
            array('%d'),
            array('%d')
        );
    }
    
    /**
     * Sanitizza i dati del cronjob
     */
    private function sanitize_cronjob_data($data) {
        return array(
            'nome_cronjob' => sanitize_text_field($data['nome_cronjob']),
            'slug_cronjob' => wcos_sanitize_slug($data['slug_cronjob']),
            'cadenza_periodica' => sanitize_text_field($data['cadenza_periodica']),
            'numero_giorni' => intval($data['numero_giorni']),
            'status_da_aggiornare' => is_array($data['status_da_aggiornare']) ? 
                implode(',', array_map('sanitize_text_field', $data['status_da_aggiornare'])) : 
                sanitize_text_field($data['status_da_aggiornare']),
            'nuovo_status' => sanitize_text_field($data['nuovo_status']),
            'oggetto_email' => sanitize_text_field($data['oggetto_email']),
            'testo_email' => wp_kses_post($data['testo_email']),
            'attivo' => isset($data['attivo']) ? intval($data['attivo']) : 1
        );
    }
}
