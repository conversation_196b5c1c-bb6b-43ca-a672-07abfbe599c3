/* WooCommerce CronJob Order Status - Admin Styles */

/* Stats Boxes */
.wcos-stats-boxes {
    display: flex;
    gap: 20px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.wcos-stat-box {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    min-width: 120px;
    flex: 1;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.wcos-stat-box h3 {
    font-size: 2.5em;
    margin: 0 0 5px 0;
    color: #0073aa;
    font-weight: 600;
    line-height: 1;
}

.wcos-stat-box p {
    margin: 0;
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

/* Status Indicators */
.wcos-status {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.wcos-status-active {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.wcos-status-inactive {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.wcos-warning {
    color: #856404;
    font-weight: 500;
}

.wcos-muted {
    color: #6c757d;
    font-style: italic;
}

/* Action Buttons */
.wcos-action-buttons {
    display: flex;
    gap: 2px;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
}

.wcos-action-buttons .button {
    padding: 4px 6px;
    min-height: 28px;
    line-height: 1.1;
    border-radius: 3px;
    font-size: 11px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    border: 1px solid #c3c4c7;
    background: #f6f7f7;
    color: #2c3338;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    font-weight: 500;
}

.wcos-action-buttons .button:hover {
    background: #f0f0f1;
    border-color: #8c8f94;
    color: #1d2327;
    transform: translateY(-1px);
}

.wcos-action-buttons .button.button-edit {
    background: #f0f6fc;
    border-color: #c3dbf0;
    color: #0073aa;
}

.wcos-action-buttons .button.button-edit:hover {
    background: #0073aa;
    border-color: #0073aa;
    color: #ffffff;
}

.wcos-action-buttons .button.button-toggle {
    background: #fff8e1;
    border-color: #ffcc02;
    color: #8a6914;
}

.wcos-action-buttons .button.button-toggle:hover {
    background: #ffcc02;
    border-color: #ffcc02;
    color: #1d2327;
}

.wcos-action-buttons .button.button-toggle.inactive {
    background: #f0f6fc;
    border-color: #c3dbf0;
    color: #0073aa;
}

.wcos-action-buttons .button.button-toggle.inactive:hover {
    background: #0073aa;
    border-color: #0073aa;
    color: #ffffff;
}

.wcos-action-buttons .button.button-run {
    background: #e8f5e8;
    border-color: #4caf50;
    color: #2e7d32;
}

.wcos-action-buttons .button.button-run:hover {
    background: #4caf50;
    border-color: #4caf50;
    color: #ffffff;
}

.wcos-action-buttons .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    line-height: 14px;
    margin-right: 3px;
    display: inline-block;
    vertical-align: middle;
}

.wcos-action-buttons .button-small .dashicons {
    margin-right: 0;
}

/* Versione solo icone per spazi ristretti */
.wcos-action-buttons.compact .button {
    padding: 4px;
    min-width: 28px;
}

.wcos-action-buttons.compact .button .button-text {
    display: none;
}

.wcos-action-buttons.compact .dashicons {
    margin-right: 0;
}

/* Info Box */
.wcos-info-box {
    background: #f0f6fc;
    border: 1px solid #c3dbf0;
    border-radius: 4px;
    padding: 15px;
    margin-top: 20px;
}

.wcos-info-box h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #0073aa;
    font-size: 16px;
}

.wcos-info-box p {
    margin-bottom: 10px;
    line-height: 1.5;
}

.wcos-info-box p:last-child {
    margin-bottom: 0;
}

/* Form Styles */
.wcos-form-container {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.wcos-form-main {
    flex: 1;
}

.wcos-form-sidebar {
    width: 300px;
    flex-shrink: 0;
}

.wcos-multiselect {
    width: 100%;
    height: 120px;
    padding: 5px;
}

/* Placeholder Buttons */
.wcos-placeholders {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 10px;
}

.wcos-placeholder-btn {
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    border-radius: 3px;
    padding: 6px 10px;
    font-size: 12px;
    font-family: 'Courier New', Courier, monospace;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #1d2327;
    text-decoration: none;
    display: inline-block;
    font-weight: 500;
    line-height: 1.2;
    min-height: 28px;
    box-sizing: border-box;
}

.wcos-placeholder-btn:hover {
    background: #0073aa;
    color: #ffffff;
    border-color: #0073aa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.wcos-placeholder-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.wcos-placeholder-btn:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* Required Field Indicator */
.required {
    color: #d63638;
    font-weight: bold;
}

/* Form Table Enhancements */
.form-table th {
    width: 200px;
    padding-left: 0;
}

.form-table td {
    padding-left: 0;
}

.form-table .description {
    margin-top: 5px;
    font-style: italic;
    color: #666;
}

/* Submit Box */
.submitbox {
    padding: 0;
    margin: 0;
}

.major-publishing-actions {
    border-top: 1px solid #dcdcde;
    padding: 10px;
    background: #f6f7f7;
    margin: 0;
}

.publishing-action {
    text-align: right;
}

/* Table Enhancements */
.wp-list-table .column-name {
    width: 16%;
}

.wp-list-table .column-slug {
    width: 10%;
}

.wp-list-table .column-frequency {
    width: 9%;
}

.wp-list-table .column-days {
    width: 5%;
}

.wp-list-table .column-status-from {
    width: 11%;
}

.wp-list-table .column-status-to {
    width: 9%;
}

.wp-list-table .column-active {
    width: 7%;
}

.wp-list-table .column-next-run {
    width: 11%;
}

.wp-list-table .column-actions {
    width: 22%;
    min-width: 200px;
}

.wp-list-table code {
    background: #f1f1f1;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 12px;
}

/* Loading States */
.wcos-loading {
    opacity: 0.6;
    pointer-events: none;
}

.wcos-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: wcos-spin 1s linear infinite;
}

@keyframes wcos-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .wcos-form-container {
        flex-direction: column;
    }
    
    .wcos-form-sidebar {
        width: 100%;
    }
}

@media (max-width: 1200px) {
    .wcos-action-buttons .button-text {
        display: none;
    }

    .wcos-action-buttons .button {
        padding: 4px;
        min-width: 28px;
    }

    .wcos-action-buttons .dashicons {
        margin-right: 0;
    }
}

@media (max-width: 782px) {
    .wcos-stats-boxes {
        flex-direction: column;
    }

    .wcos-stat-box {
        min-width: auto;
    }

    .wcos-action-buttons {
        justify-content: center;
        flex-wrap: wrap;
        gap: 2px;
    }

    .wcos-action-buttons .button {
        font-size: 10px;
        padding: 3px;
        min-height: 24px;
        min-width: 24px;
    }

    .wcos-action-buttons .dashicons {
        font-size: 12px;
        width: 12px;
        height: 12px;
        margin-right: 0;
    }

    .wcos-action-buttons .button-text {
        display: none;
    }

    .wp-list-table .column-actions {
        width: auto;
        min-width: 80px;
    }

    .wcos-placeholders {
        justify-content: center;
    }

    .wcos-placeholder-btn {
        font-size: 10px;
        padding: 3px 6px;
    }
}

@media (max-width: 600px) {
    .wcos-form-container {
        gap: 10px;
    }
    
    .wcos-stats-boxes {
        gap: 10px;
    }
    
    .wcos-stat-box {
        padding: 15px;
    }
    
    .wcos-stat-box h3 {
        font-size: 2em;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .wcos-stat-box {
        background: #1e1e1e;
        border-color: #3c434a;
        color: #f0f0f1;
    }
    
    .wcos-stat-box h3 {
        color: #72aee6;
    }
    
    .wcos-stat-box p {
        color: #a7aaad;
    }
    
    .wcos-info-box {
        background: #1e1e1e;
        border-color: #3c434a;
        color: #f0f0f1;
    }
    
    .wcos-info-box h3 {
        color: #72aee6;
    }
    
    .wcos-placeholder-btn {
        background: #2c3338;
        border-color: #3c434a;
        color: #f0f0f1;
    }

    .wcos-placeholder-btn:hover {
        background: #72aee6;
        border-color: #72aee6;
        color: #1e1e1e;
    }

    .wcos-placeholder-btn:focus {
        outline-color: #72aee6;
    }
}

/* Print Styles */
@media print {
    .wcos-action-buttons,
    .page-title-action,
    .submitbox {
        display: none !important;
    }
    
    .wcos-stats-boxes {
        break-inside: avoid;
    }
    
    .postbox {
        break-inside: avoid;
        border: 1px solid #000;
        margin-bottom: 20px;
    }
}
