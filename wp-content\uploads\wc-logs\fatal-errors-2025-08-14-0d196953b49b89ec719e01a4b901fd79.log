2025-08-14T07:39:19+00:00 CRITICAL Uncaught ArgumentCountError: Too few arguments to function Woo_Cronjob_Email::get_default_placeholders(), 3 passed in C:\laragon\www\test\wp-includes\class-wp-hook.php on line 326 and exactly 4 expected in C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\includes\class-woo-cronjob-email.php:229 CONTEXT: {"error":{"type":1,"file":"C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\includes\class-woo-cronjob-email.php","line":229},"remote-logging":true,"backtrace":["#0 C:\laragon\www\test\wp-includes\class-wp-hook.php(326): Woo_Cronjob_Email->get_default_placeholders(Array, Object(Automattic\WooCommerce\Admin\Overrides\Order), 'on-hold')n#1 C:\laragon\www\test\wp-includes\plugin.php(205): WP_Hook->apply_filters(Array, Array)n#2 C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\includes\class-woo-cronjob-email.php(101): apply_filters('wcos_email_plac...', Array, Object(Automattic\WooCommerce\Admin\Overrides\Order), 'on-hold', 'processing')n#3 C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\includes\class-woo-cronjob-email.php(64): Woo_Cronjob_Email->get_placeholders(Object(Automattic\WooCommerce\Admin\Overrides\Order), 'on-hold', 'processing')n#4 C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\includes\class-woo-cronjob-email.php(30): Woo_Cronjob_Email->replace_placeholders('test cron', Object(Automattic\WooCommerce\Admin\Overrides\Order), 'on-hold', 'processing')n#5 C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\includes\class-woo-cronjob-cron.php(111): Woo_Cronjob_Email->send_customer_notification(Object(Automattic\WooCommerce\Admin\Overrides\Order), 'test cron', 'testiamo il cro...', 'on-hold', 'processing')n#6 C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\includes\class-woo-cronjob-cron.php(203): Woo_Cronjob_Cron->execute_cronjob(Object(stdClass))n#7 C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\includes\class-woo-cronjob-admin.php(252): Woo_Cronjob_Cron->manual_execute(1)n#8 C:\laragon\www\test\wp-includes\class-wp-hook.php(324): Woo_Cronjob_Admin->handle_run_cronjob('')n#9 C:\laragon\www\test\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)n#10 C:\laragon\www\test\wp-includes\plugin.php(517): WP_Hook->do_action(Array)n#11 C:\laragon\www\test\wp-admin\admin-post.php(82): do_action('admin_post_wcos...')n#12 {main}n  thrown"]}
2025-08-14T07:43:01+00:00 CRITICAL Uncaught Error: Call to undefined method Automattic\WooCommerce\Admin\Overrides\OrderRefund::get_billing_first_name() in C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\debug-cronjob.php:127 CONTEXT: {"error":{"type":1,"file":"C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\debug-cronjob.php","line":127},"remote-logging":true,"backtrace":["#0 {main}n  thrown"]}
2025-08-14T07:43:04+00:00 CRITICAL Uncaught Error: Call to undefined method Automattic\WooCommerce\Admin\Overrides\OrderRefund::get_billing_first_name() in C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\debug-cronjob.php:127 CONTEXT: {"error":{"type":1,"file":"C:\laragon\www\test\wp-content\plugins\woo-cronjob-order-status\debug-cronjob.php","line":127},"remote-logging":true,"backtrace":["#0 {main}n  thrown"]}
