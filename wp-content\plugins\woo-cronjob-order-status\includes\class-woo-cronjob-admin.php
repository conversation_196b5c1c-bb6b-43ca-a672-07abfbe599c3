<?php
if (!defined('ABSPATH')) {
    exit;
}

class Woo_Cronjob_Admin {
    private $db;
    
    public function __construct() {
        $this->db = new Woo_Cronjob_DB();
        
        // Aggiungi menu
        add_action('admin_menu', [$this, 'add_admin_menu']);
        
        // Registra assets
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
        
        // Gestisci le azioni POST
        add_action('admin_post_wcos_save_cronjob', [$this, 'handle_save_cronjob']);
        add_action('admin_post_wcos_delete_cronjob', [$this, 'handle_delete_cronjob']);
        add_action('admin_post_wcos_toggle_cronjob', [$this, 'handle_toggle_cronjob']);

        // Gestisci le azioni AJAX
        add_action('wp_ajax_wcos_test_cronjob', [$this, 'ajax_test_cronjob']);
        add_action('wp_ajax_wcos_generate_slug', [$this, 'ajax_generate_slug']);

        // Aggiungi notice per messaggi
        add_action('admin_notices', [$this, 'show_admin_notices']);
    }
    
    public function add_admin_menu() {
        add_submenu_page(
            'tools.php',
            __('Crea CronJob', 'woo-cronjob-order-status'),
            __('Crea CronJob', 'woo-cronjob-order-status'),
            'manage_options',
            'wcos-cronjobs',
            [$this, 'render_admin_page']
        );
    }
    
    public function enqueue_admin_assets($hook) {
        if ('tools_page_wcos-cronjobs' !== $hook) {
            return;
        }
        
        // CSS
        wp_enqueue_style(
            'wcos-admin-style',
            WCOS_PLUGIN_URL . 'assets/css/admin.css',
            [],
            WCOS_VERSION
        );
        
        // JS
        wp_enqueue_script(
            'wcos-admin-script',
            WCOS_PLUGIN_URL . 'assets/js/admin.js',
            ['jquery'],
            WCOS_VERSION,
            true
        );
        
        // Localizza script
        wp_localize_script('wcos-admin-script', 'wcos_admin', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wcos_admin_nonce'),
            'strings' => [
                'confirm_delete' => __('Sei sicuro di voler eliminare questo cronjob?', 'woo-cronjob-order-status'),
                'slug_generated' => __('Slug generato automaticamente', 'woo-cronjob-order-status')
            ]
        ]);
    }
    
    public function render_admin_page() {
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'list';
        $cronjob_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        switch ($action) {
            case 'new':
                $this->render_cronjob_form();
                break;
            case 'edit':
                $this->render_cronjob_form($cronjob_id);
                break;
            default:
                $this->render_cronjob_list();
                break;
        }
    }
    
    private function render_cronjob_list() {
        $cronjobs = $this->db->get_all_cronjobs();
        include WCOS_PLUGIN_DIR . 'templates/admin-cronjob-list.php';
    }
    
    private function render_cronjob_form($cronjob_id = 0) {
        $cronjob = null;
        $is_edit = false;
        
        if ($cronjob_id > 0) {
            $cronjob = $this->db->get_cronjob($cronjob_id);
            $is_edit = true;
            
            if (!$cronjob) {
                wp_die(__('CronJob non trovato', 'woo-cronjob-order-status'));
            }
        }
        
        $order_statuses = wcos_get_woocommerce_order_statuses();
        $frequencies = wcos_get_cron_frequencies();
        
        include WCOS_PLUGIN_DIR . 'templates/admin-cronjob-form.php';
    }
    
    public function handle_save_cronjob() {
        // Verifica nonce
        if (!wp_verify_nonce($_POST['wcos_nonce'], 'wcos_save_cronjob')) {
            wp_die(__('Errore di sicurezza', 'woo-cronjob-order-status'));
        }
        
        // Verifica permessi
        if (!current_user_can('manage_options')) {
            wp_die(__('Non hai i permessi per eseguire questa azione', 'woo-cronjob-order-status'));
        }
        
        $cronjob_id = isset($_POST['cronjob_id']) ? intval($_POST['cronjob_id']) : 0;
        $is_edit = $cronjob_id > 0;
        
        // Validazione dati
        $errors = $this->validate_cronjob_data($_POST);
        
        if (!empty($errors)) {
            $error_message = implode('<br>', $errors);
            $this->redirect_with_message($error_message, 'error', $is_edit ? 'edit' : 'new', $cronjob_id);
            return;
        }
        
        // Prepara dati
        $data = [
            'nome_cronjob' => sanitize_text_field($_POST['nome_cronjob']),
            'slug_cronjob' => wcos_sanitize_slug($_POST['slug_cronjob']),
            'cadenza_periodica' => sanitize_text_field($_POST['cadenza_periodica']),
            'numero_giorni' => intval($_POST['numero_giorni']),
            'status_da_aggiornare' => $_POST['status_da_aggiornare'],
            'nuovo_status' => sanitize_text_field($_POST['nuovo_status']),
            'oggetto_email' => sanitize_text_field($_POST['oggetto_email']),
            'testo_email' => wp_kses_post($_POST['testo_email']),
            'attivo' => isset($_POST['attivo']) ? 1 : 0
        ];
        
        if ($is_edit) {
            $result = $this->db->update_cronjob($cronjob_id, $data);
            $message = __('CronJob aggiornato con successo', 'woo-cronjob-order-status');
        } else {
            $result = $this->db->insert_cronjob($data);
            $message = __('CronJob creato con successo', 'woo-cronjob-order-status');
        }
        
        if (is_wp_error($result)) {
            // Log dell'errore per debug
            wcos_log('Failed to save cronjob: ' . $result->get_error_message(), 'error');
            wcos_log('Cronjob data: ' . print_r($data, true), 'error');

            $this->redirect_with_message($result->get_error_message(), 'error', $is_edit ? 'edit' : 'new', $cronjob_id);
            return;
        }
        
        // Programma o aggiorna il cron job
        $this->schedule_cronjob($data);
        
        $this->redirect_with_message($message, 'success');
    }
    
    public function handle_delete_cronjob() {
        // Verifica nonce
        if (!wp_verify_nonce($_GET['nonce'], 'wcos_delete_cronjob')) {
            wp_die(__('Errore di sicurezza', 'woo-cronjob-order-status'));
        }
        
        // Verifica permessi
        if (!current_user_can('manage_options')) {
            wp_die(__('Non hai i permessi per eseguire questa azione', 'woo-cronjob-order-status'));
        }
        
        $cronjob_id = intval($_GET['id']);
        $cronjob = $this->db->get_cronjob($cronjob_id);
        
        if (!$cronjob) {
            $this->redirect_with_message(__('CronJob non trovato', 'woo-cronjob-order-status'), 'error');
            return;
        }
        
        // Rimuovi il cron job programmato
        wp_clear_scheduled_hook('wcos_execute_cronjob_' . $cronjob->slug_cronjob);
        
        // Elimina dal database
        $result = $this->db->delete_cronjob($cronjob_id);
        
        if (is_wp_error($result)) {
            $this->redirect_with_message($result->get_error_message(), 'error');
            return;
        }
        
        $this->redirect_with_message(__('CronJob eliminato con successo', 'woo-cronjob-order-status'), 'success');
    }
    
    public function handle_toggle_cronjob() {
        // Verifica nonce
        if (!wp_verify_nonce($_GET['nonce'], 'wcos_toggle_cronjob')) {
            wp_die(__('Errore di sicurezza', 'woo-cronjob-order-status'));
        }
        
        // Verifica permessi
        if (!current_user_can('manage_options')) {
            wp_die(__('Non hai i permessi per eseguire questa azione', 'woo-cronjob-order-status'));
        }
        
        $cronjob_id = intval($_GET['id']);
        $result = $this->db->toggle_cronjob_status($cronjob_id);
        
        if ($result === false) {
            $this->redirect_with_message(__('Errore durante l\'aggiornamento dello stato', 'woo-cronjob-order-status'), 'error');
            return;
        }
        
        $this->redirect_with_message(__('Stato cronjob aggiornato', 'woo-cronjob-order-status'), 'success');
    }
    
    private function validate_cronjob_data($data) {
        $errors = [];
        
        if (empty($data['nome_cronjob'])) {
            $errors[] = __('Il nome del cronjob è obbligatorio', 'woo-cronjob-order-status');
        }
        
        if (empty($data['slug_cronjob'])) {
            $errors[] = __('Lo slug del cronjob è obbligatorio', 'woo-cronjob-order-status');
        } else {
            $cronjob_id = isset($data['cronjob_id']) ? intval($data['cronjob_id']) : 0;
            if ($this->db->slug_exists($data['slug_cronjob'], $cronjob_id)) {
                $errors[] = __('Lo slug del cronjob esiste già', 'woo-cronjob-order-status');
            }
        }
        
        if (empty($data['cadenza_periodica'])) {
            $errors[] = __('La cadenza periodica è obbligatoria', 'woo-cronjob-order-status');
        }
        
        if (empty($data['numero_giorni']) || intval($data['numero_giorni']) <= 0) {
            $errors[] = __('Il numero di giorni deve essere maggiore di zero', 'woo-cronjob-order-status');
        }
        
        if (empty($data['status_da_aggiornare'])) {
            $errors[] = __('Seleziona almeno uno status da aggiornare', 'woo-cronjob-order-status');
        }
        
        if (empty($data['nuovo_status'])) {
            $errors[] = __('Il nuovo status è obbligatorio', 'woo-cronjob-order-status');
        }
        
        if (empty($data['oggetto_email'])) {
            $errors[] = __('L\'oggetto email è obbligatorio', 'woo-cronjob-order-status');
        }
        
        if (empty($data['testo_email'])) {
            $errors[] = __('Il testo email è obbligatorio', 'woo-cronjob-order-status');
        }
        
        return $errors;
    }
    
    private function schedule_cronjob($data) {
        $hook = 'wcos_execute_cronjob_' . $data['slug_cronjob'];
        
        // Rimuovi il cron esistente
        wp_clear_scheduled_hook($hook);
        
        // Programma il nuovo cron se attivo
        if ($data['attivo']) {
            wp_schedule_event(time(), $data['cadenza_periodica'], $hook);
        }
    }
    
    private function redirect_with_message($message, $type = 'success', $action = 'list', $id = 0) {
        $url = admin_url('tools.php?page=wcos-cronjobs');
        
        if ($action !== 'list') {
            $url .= '&action=' . $action;
            if ($id > 0) {
                $url .= '&id=' . $id;
            }
        }
        
        $url .= '&message=' . urlencode($message) . '&message_type=' . $type;
        
        wp_redirect($url);
        exit;
    }
    
    public function show_admin_notices() {
        if (isset($_GET['message']) && isset($_GET['message_type'])) {
            $message = sanitize_text_field($_GET['message']);
            $type = sanitize_text_field($_GET['message_type']);

            $class = $type === 'error' ? 'notice-error' : 'notice-success';

            echo '<div class="notice ' . $class . ' is-dismissible"><p>' . esc_html($message) . '</p></div>';
        }
    }

    /**
     * AJAX: Test cronjob execution
     */
    public function ajax_test_cronjob() {
        // Verifica nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcos_admin_nonce')) {
            wp_die(__('Errore di sicurezza', 'woo-cronjob-order-status'));
        }

        // Verifica permessi
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Non hai i permessi per eseguire questa azione', 'woo-cronjob-order-status')]);
        }

        $cronjob_id = intval($_POST['cronjob_id']);

        // Esegui il cronjob manualmente
        $cron = new Woo_Cronjob_Cron();
        $result = $cron->manual_execute($cronjob_id);

        if (is_wp_error($result)) {
            wp_send_json_error(['message' => $result->get_error_message()]);
        }

        wp_send_json_success(['message' => __('Cronjob eseguito con successo', 'woo-cronjob-order-status')]);
    }

    /**
     * AJAX: Generate slug from name
     */
    public function ajax_generate_slug() {
        // Verifica nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcos_admin_nonce')) {
            wp_die(__('Errore di sicurezza', 'woo-cronjob-order-status'));
        }

        $name = sanitize_text_field($_POST['name']);
        $slug = wcos_sanitize_slug($name);

        wp_send_json_success(['slug' => $slug]);
    }
}
