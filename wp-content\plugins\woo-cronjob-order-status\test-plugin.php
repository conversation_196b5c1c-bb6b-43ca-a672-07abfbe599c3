<?php
/**
 * Test file for WooCommerce CronJob Order Status Plugin
 * 
 * This file can be used to test plugin functionality.
 * Access it via: /wp-content/plugins/woo-cronjob-order-status/test-plugin.php
 * 
 * IMPORTANT: Remove this file in production!
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('../../../wp-load.php');
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    wp_die('WooCommerce is not active');
}

// Check if our plugin is active
if (!defined('WCOS_VERSION')) {
    wp_die('WooCommerce CronJob Order Status plugin is not active');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>WCOS Plugin Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>WooCommerce CronJob Order Status - Test Page</h1>
    
    <div class="test-section">
        <h2>Plugin Information</h2>
        <p><strong>Version:</strong> <?php echo WCOS_VERSION; ?></p>
        <p><strong>Plugin Directory:</strong> <?php echo WCOS_PLUGIN_DIR; ?></p>
        <p><strong>Plugin URL:</strong> <?php echo WCOS_PLUGIN_URL; ?></p>
        <p><strong>Table Name:</strong> <?php echo WCOS_TABLE_NAME; ?></p>
    </div>
    
    <div class="test-section">
        <h2>Database Test</h2>
        <?php
        try {
            $db = new Woo_Cronjob_DB();
            $cronjobs = $db->get_all_cronjobs();
            echo '<p class="success">✓ Database connection successful</p>';
            echo '<p><strong>Total CronJobs:</strong> ' . count($cronjobs) . '</p>';
            
            if (!empty($cronjobs)) {
                echo '<h3>Existing CronJobs:</h3>';
                echo '<ul>';
                foreach ($cronjobs as $cronjob) {
                    echo '<li>' . esc_html($cronjob->nome_cronjob) . ' (' . esc_html($cronjob->slug_cronjob) . ') - ';
                    echo $cronjob->attivo ? '<span class="success">Active</span>' : '<span class="error">Inactive</span>';
                    echo '</li>';
                }
                echo '</ul>';
            }
        } catch (Exception $e) {
            echo '<p class="error">✗ Database error: ' . esc_html($e->getMessage()) . '</p>';
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>WooCommerce Integration Test</h2>
        <?php
        $order_statuses = wcos_get_woocommerce_order_statuses();
        if (!empty($order_statuses)) {
            echo '<p class="success">✓ WooCommerce order statuses loaded</p>';
            echo '<p><strong>Available Statuses:</strong></p>';
            echo '<ul>';
            foreach ($order_statuses as $key => $label) {
                echo '<li>' . esc_html($key) . ' - ' . esc_html($label) . '</li>';
            }
            echo '</ul>';
        } else {
            echo '<p class="error">✗ No WooCommerce order statuses found</p>';
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>Cron Frequencies Test</h2>
        <?php
        $frequencies = wcos_get_cron_frequencies();
        if (!empty($frequencies)) {
            echo '<p class="success">✓ Cron frequencies loaded</p>';
            echo '<ul>';
            foreach ($frequencies as $key => $label) {
                echo '<li>' . esc_html($key) . ' - ' . esc_html($label) . '</li>';
            }
            echo '</ul>';
        } else {
            echo '<p class="error">✗ No cron frequencies found</p>';
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>WordPress Cron Test</h2>
        <?php
        $crons = _get_cron_array();
        $wcos_crons = array();
        
        foreach ($crons as $timestamp => $cron) {
            foreach ($cron as $hook => $events) {
                if (strpos($hook, 'wcos_') === 0) {
                    $wcos_crons[] = array(
                        'hook' => $hook,
                        'timestamp' => $timestamp,
                        'next_run' => date('Y-m-d H:i:s', $timestamp)
                    );
                }
            }
        }
        
        if (!empty($wcos_crons)) {
            echo '<p class="success">✓ Found ' . count($wcos_crons) . ' scheduled WCOS cron jobs</p>';
            echo '<ul>';
            foreach ($wcos_crons as $cron) {
                echo '<li>' . esc_html($cron['hook']) . ' - Next run: ' . esc_html($cron['next_run']) . '</li>';
            }
            echo '</ul>';
        } else {
            echo '<p class="info">ℹ No WCOS cron jobs currently scheduled</p>';
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>Email Placeholders Test</h2>
        <?php
        $placeholders = Woo_Cronjob_Email::get_available_placeholders();
        if (!empty($placeholders)) {
            echo '<p class="success">✓ Email placeholders loaded</p>';
            echo '<p><strong>Available Placeholders:</strong></p>';
            echo '<ul>';
            foreach ($placeholders as $placeholder => $description) {
                echo '<li><code>' . esc_html($placeholder) . '</code> - ' . esc_html($description) . '</li>';
            }
            echo '</ul>';
        } else {
            echo '<p class="error">✗ No email placeholders found</p>';
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>Sample Orders Test</h2>
        <?php
        $orders = wc_get_orders(array(
            'limit' => 5,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        if (!empty($orders)) {
            echo '<p class="success">✓ Found ' . count($orders) . ' recent orders</p>';
            echo '<ul>';
            foreach ($orders as $order) {
                echo '<li>Order #' . $order->get_id() . ' - ' . $order->get_status() . ' - ' . $order->get_date_created()->date('Y-m-d H:i:s') . '</li>';
            }
            echo '</ul>';
        } else {
            echo '<p class="info">ℹ No orders found in WooCommerce</p>';
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>Plugin Files Test</h2>
        <?php
        $required_files = array(
            'includes/class-woo-cronjob-admin.php',
            'includes/class-woo-cronjob-db.php',
            'includes/class-woo-cronjob-cron.php',
            'includes/class-woo-cronjob-email.php',
            'templates/admin-cronjob-list.php',
            'templates/admin-cronjob-form.php',
            'assets/css/admin.css',
            'assets/js/admin.js'
        );
        
        $missing_files = array();
        foreach ($required_files as $file) {
            if (!file_exists(WCOS_PLUGIN_DIR . $file)) {
                $missing_files[] = $file;
            }
        }
        
        if (empty($missing_files)) {
            echo '<p class="success">✓ All required plugin files are present</p>';
        } else {
            echo '<p class="error">✗ Missing files:</p>';
            echo '<ul>';
            foreach ($missing_files as $file) {
                echo '<li>' . esc_html($file) . '</li>';
            }
            echo '</ul>';
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>WordPress Environment</h2>
        <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
        <p><strong>WooCommerce Version:</strong> <?php echo defined('WC_VERSION') ? WC_VERSION : 'Unknown'; ?></p>
        <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
        <p><strong>MySQL Version:</strong> <?php echo $GLOBALS['wpdb']->db_version(); ?></p>
        <p><strong>WordPress Debug:</strong> <?php echo WP_DEBUG ? 'Enabled' : 'Disabled'; ?></p>
        <p><strong>WP Cron:</strong> <?php echo defined('DISABLE_WP_CRON') && DISABLE_WP_CRON ? 'Disabled' : 'Enabled'; ?></p>
    </div>
    
    <div class="test-section">
        <h2>Quick Actions</h2>
        <p><a href="<?php echo admin_url('tools.php?page=wcos-cronjobs'); ?>">Go to CronJob Management</a></p>
        <p><a href="<?php echo admin_url('admin.php?page=wc-status&tab=logs'); ?>">View WooCommerce Logs</a></p>
        <p><a href="<?php echo admin_url('tools.php?page=cron'); ?>">View WordPress Cron (if WP Crontrol is installed)</a></p>
    </div>
    
    <p><small><strong>Note:</strong> This test file should be removed in production environments for security reasons.</small></p>
</body>
</html>
