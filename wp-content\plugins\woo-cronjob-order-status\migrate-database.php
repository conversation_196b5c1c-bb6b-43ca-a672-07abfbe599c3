<?php
/**
 * Database Migration for WooCommerce CronJob Order Status Plugin
 * 
 * This file migrates the database from single 'numero_giorni' to range 'giorni_da' and 'giorni_a'
 * Access it via: /wp-content/plugins/woo-cronjob-order-status/migrate-database.php
 * 
 * IMPORTANT: Remove this file after migration!
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('../../../wp-load.php');
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

// Check if our plugin is active
if (!defined('WCOS_VERSION')) {
    wp_die('WooCommerce CronJob Order Status plugin is not active');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>WCOS Database Migration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .migration-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .migrate-button { background: #0073aa; color: white; padding: 10px 20px; border: none; cursor: pointer; margin: 10px 0; }
        .migrate-button:hover { background: #005a87; }
        .migrate-button:disabled { background: #ccc; cursor: not-allowed; }
    </style>
</head>
<body>
    <h1>WooCommerce CronJob Order Status - Database Migration</h1>
    
    <div class="migration-section">
        <h2>Migration Status</h2>
        <?php
        global $wpdb;
        $table_name = $wpdb->prefix . WCOS_TABLE_NAME;
        
        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
        
        if (!$table_exists) {
            echo '<p class="error">✗ Tabella non trovata. Attiva prima il plugin.</p>';
            exit;
        }
        
        // Check current table structure
        $columns = $wpdb->get_results("DESCRIBE $table_name");
        $column_names = array_column($columns, 'Field');
        
        $has_numero_giorni = in_array('numero_giorni', $column_names);
        $has_giorni_da = in_array('giorni_da', $column_names);
        $has_giorni_a = in_array('giorni_a', $column_names);
        
        echo '<h3>Struttura Tabella Attuale:</h3>';
        echo '<ul>';
        foreach ($column_names as $column) {
            $status = '';
            if ($column === 'numero_giorni') {
                $status = $has_giorni_da && $has_giorni_a ? ' <span class="warning">(da migrare)</span>' : ' <span class="info">(vecchio formato)</span>';
            } elseif ($column === 'giorni_da' || $column === 'giorni_a') {
                $status = ' <span class="success">(nuovo formato)</span>';
            }
            echo '<li>' . esc_html($column) . $status . '</li>';
        }
        echo '</ul>';
        
        // Migration status
        if ($has_giorni_da && $has_giorni_a) {
            if ($has_numero_giorni) {
                echo '<p class="warning">⚠ Migrazione parziale: nuove colonne presenti ma vecchia colonna ancora esistente</p>';
                $migration_needed = 'cleanup';
            } else {
                echo '<p class="success">✓ Migrazione completata</p>';
                $migration_needed = 'none';
            }
        } else {
            echo '<p class="error">✗ Migrazione necessaria: mancano le nuove colonne</p>';
            $migration_needed = 'full';
        }
        ?>
    </div>
    
    <div class="migration-section">
        <h2>Data Preview</h2>
        <?php
        $cronjobs = $wpdb->get_results("SELECT * FROM $table_name LIMIT 5");
        
        if (empty($cronjobs)) {
            echo '<p class="info">ℹ Nessun cronjob presente nel database</p>';
        } else {
            echo '<table border="1" cellpadding="5" style="width: 100%; border-collapse: collapse;">';
            echo '<tr>';
            echo '<th>ID</th><th>Nome</th>';
            if ($has_numero_giorni) echo '<th>Numero Giorni (OLD)</th>';
            if ($has_giorni_da) echo '<th>Giorni Da (NEW)</th>';
            if ($has_giorni_a) echo '<th>Giorni A (NEW)</th>';
            echo '<th>Status</th>';
            echo '</tr>';
            
            foreach ($cronjobs as $cronjob) {
                echo '<tr>';
                echo '<td>' . $cronjob->id . '</td>';
                echo '<td>' . esc_html($cronjob->nome_cronjob) . '</td>';
                if ($has_numero_giorni) echo '<td>' . (isset($cronjob->numero_giorni) ? $cronjob->numero_giorni : 'N/A') . '</td>';
                if ($has_giorni_da) echo '<td>' . (isset($cronjob->giorni_da) ? $cronjob->giorni_da : 'N/A') . '</td>';
                if ($has_giorni_a) echo '<td>' . (isset($cronjob->giorni_a) ? $cronjob->giorni_a : 'N/A') . '</td>';
                echo '<td>' . ($cronjob->attivo ? 'Attivo' : 'Inattivo') . '</td>';
                echo '</tr>';
            }
            
            echo '</table>';
            
            if (count($cronjobs) === 5) {
                $total = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
                echo '<p><em>Mostrati 5 di ' . $total . ' record totali</em></p>';
            }
        }
        ?>
    </div>
    
    <?php if ($migration_needed !== 'none'): ?>
    <div class="migration-section">
        <h2>Migration Actions</h2>
        
        <?php if ($migration_needed === 'full'): ?>
            <p>La migrazione aggiungerà le nuove colonne <code>giorni_da</code> e <code>giorni_a</code> e convertirà i dati esistenti.</p>
            <p><strong>Strategia di conversione:</strong></p>
            <ul>
                <li><code>giorni_da = 0</code> (da oggi)</li>
                <li><code>giorni_a = numero_giorni</code> (fino al valore originale)</li>
            </ul>
            
            <form method="post">
                <input type="hidden" name="action" value="migrate_full">
                <button type="submit" class="migrate-button">Esegui Migrazione Completa</button>
            </form>
            
        <?php elseif ($migration_needed === 'cleanup'): ?>
            <p>La migrazione è stata completata ma la vecchia colonna <code>numero_giorni</code> è ancora presente.</p>
            <p><strong>Azione:</strong> Rimuovere la colonna obsoleta per completare la migrazione.</p>
            
            <form method="post">
                <input type="hidden" name="action" value="cleanup_old_column">
                <button type="submit" class="migrate-button">Rimuovi Colonna Obsoleta</button>
            </form>
        <?php endif; ?>
        
        <h3>Backup Consigliato</h3>
        <p class="warning">⚠ <strong>IMPORTANTE:</strong> Esegui un backup del database prima di procedere con la migrazione!</p>
        
        <h3>SQL Commands (Manual)</h3>
        <p>Se preferisci eseguire la migrazione manualmente, usa questi comandi SQL:</p>
        
        <?php if ($migration_needed === 'full'): ?>
            <pre><?php
echo "-- Aggiungi nuove colonne
ALTER TABLE `{$table_name}` 
ADD COLUMN `giorni_da` int(11) NOT NULL DEFAULT 0 AFTER `cadenza_periodica`,
ADD COLUMN `giorni_a` int(11) NOT NULL DEFAULT 0 AFTER `giorni_da`;

-- Migra i dati esistenti
UPDATE `{$table_name}` 
SET `giorni_da` = 0, `giorni_a` = `numero_giorni` 
WHERE `giorni_da` = 0 AND `giorni_a` = 0;

-- (Opzionale) Rimuovi la vecchia colonna
-- ALTER TABLE `{$table_name}` DROP COLUMN `numero_giorni`;";
            ?></pre>
        <?php elseif ($migration_needed === 'cleanup'): ?>
            <pre><?php
echo "-- Rimuovi la vecchia colonna
ALTER TABLE `{$table_name}` DROP COLUMN `numero_giorni`;";
            ?></pre>
        <?php endif; ?>
    </div>
    <?php endif; ?>
    
    <?php
    // Handle form submissions
    if ($_POST['action'] ?? '') {
        echo '<div class="migration-section">';
        echo '<h2>Migration Result</h2>';
        
        switch ($_POST['action']) {
            case 'migrate_full':
                echo '<p>Esecuzione migrazione completa...</p>';
                
                try {
                    // Add new columns
                    $wpdb->query("ALTER TABLE `{$table_name}` 
                                 ADD COLUMN `giorni_da` int(11) NOT NULL DEFAULT 0 AFTER `cadenza_periodica`,
                                 ADD COLUMN `giorni_a` int(11) NOT NULL DEFAULT 0 AFTER `giorni_da`");
                    
                    echo '<p class="success">✓ Nuove colonne aggiunte</p>';
                    
                    // Migrate existing data
                    $result = $wpdb->query("UPDATE `{$table_name}` 
                                           SET `giorni_da` = 0, `giorni_a` = `numero_giorni` 
                                           WHERE `giorni_da` = 0 AND `giorni_a` = 0");
                    
                    echo '<p class="success">✓ Dati migrati: ' . $result . ' record aggiornati</p>';
                    echo '<p class="info">ℹ La vecchia colonna `numero_giorni` è stata mantenuta per sicurezza. Puoi rimuoverla dopo aver verificato che tutto funzioni correttamente.</p>';
                    
                } catch (Exception $e) {
                    echo '<p class="error">✗ Errore durante la migrazione: ' . esc_html($e->getMessage()) . '</p>';
                }
                break;
                
            case 'cleanup_old_column':
                echo '<p>Rimozione colonna obsoleta...</p>';
                
                try {
                    $wpdb->query("ALTER TABLE `{$table_name}` DROP COLUMN `numero_giorni`");
                    echo '<p class="success">✓ Colonna obsoleta rimossa con successo</p>';
                    
                } catch (Exception $e) {
                    echo '<p class="error">✗ Errore durante la rimozione: ' . esc_html($e->getMessage()) . '</p>';
                }
                break;
        }
        
        echo '</div>';
        echo '<script>setTimeout(function(){ location.reload(); }, 2000);</script>';
    }
    ?>
    
    <p><small><strong>Note:</strong> This migration file should be removed after completing the migration for security reasons.</small></p>
</body>
</html>
