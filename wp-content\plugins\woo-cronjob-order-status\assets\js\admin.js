/**
 * WooCommerce CronJob Order Status - Admin JavaScript
 */

(function($) {
    'use strict';
    
    // Oggetto principale per gestire le funzionalità admin
    var WCOSAdmin = {
        
        init: function() {
            this.bindEvents();
            this.initTooltips();
            this.initFormValidation();
        },
        
        bindEvents: function() {
            // Genera slug automaticamente
            $(document).on('click', '#generate_slug', this.generateSlug);
            
            // Auto-genera slug quando si digita il nome
            $(document).on('input', '#nome_cronjob', this.autoGenerateSlug);
            
            // Inserisci placeholder nel testo email
            $(document).on('click', '.wcos-placeholder-btn', this.insertPlaceholder);
            
            // Conferma eliminazione
            $(document).on('click', 'a[href*="wcos_delete_cronjob"]', this.confirmDelete);
            
            // Validazione form in tempo reale
            $(document).on('input change', '.wcos-form input, .wcos-form select, .wcos-form textarea', this.validateField);
            
            // Anteprima email
            $(document).on('click', '#preview_email', this.previewEmail);
            
            // Test cronjob
            $(document).on('click', '#test_cronjob', this.testCronjob);

            // Range giorni preview
            $(document).on('input', '#giorni_da, #giorni_a', this.updateRangePreview);

            // Status selection helpers
            $(document).on('click', '#select-all-statuses', this.selectAllStatuses);
            $(document).on('click', '#deselect-all-statuses', this.deselectAllStatuses);
        },
        
        generateSlug: function(e) {
            e.preventDefault();
            
            var nome = $('#nome_cronjob').val().trim();
            if (!nome) {
                alert(wcos_admin.strings.no_name_for_slug || 'Inserisci prima il nome del cronjob');
                return;
            }
            
            var slug = WCOSAdmin.createSlug(nome);
            $('#slug_cronjob').val(slug).trigger('change');
            
            // Mostra messaggio di conferma
            WCOSAdmin.showNotice(wcos_admin.strings.slug_generated || 'Slug generato automaticamente', 'success');
        },
        
        autoGenerateSlug: function() {
            var slugField = $('#slug_cronjob');
            
            // Solo se il campo slug è vuoto o non è stato modificato manualmente
            if (slugField.val() === '' || slugField.data('auto-generated')) {
                var nome = $(this).val().trim();
                if (nome) {
                    var slug = WCOSAdmin.createSlug(nome);
                    slugField.val(slug).data('auto-generated', true);
                }
            }
        },
        
        createSlug: function(text) {
            return text.toLowerCase()
                      .replace(/[àáâãäå]/g, 'a')
                      .replace(/[èéêë]/g, 'e')
                      .replace(/[ìíîï]/g, 'i')
                      .replace(/[òóôõö]/g, 'o')
                      .replace(/[ùúûü]/g, 'u')
                      .replace(/[ñ]/g, 'n')
                      .replace(/[ç]/g, 'c')
                      .replace(/[^a-z0-9\s-]/g, '')
                      .replace(/\s+/g, '-')
                      .replace(/-+/g, '-')
                      .replace(/^-+|-+$/g, '');
        },
        
        insertPlaceholder: function(e) {
            e.preventDefault();
            e.stopPropagation();

            var $button = $(this);
            var placeholder = $button.data('placeholder');

            // Previeni doppi click
            if ($button.hasClass('wcos-inserting')) {
                return;
            }

            $button.addClass('wcos-inserting');

            var editor = tinymce.get('testo_email');
            var inserted = false;

            if (editor && !editor.isHidden() && editor.getBody()) {
                // Inserisci nel TinyMCE editor
                try {
                    editor.insertContent(placeholder + ' ');
                    editor.focus();
                    inserted = true;
                } catch (err) {
                    console.log('TinyMCE insert failed, falling back to textarea');
                }
            }

            if (!inserted) {
                // Inserisci nella textarea
                var textarea = $('#testo_email')[0];
                if (textarea) {
                    var cursorPos = textarea.selectionStart || 0;
                    var textBefore = textarea.value.substring(0, cursorPos);
                    var textAfter = textarea.value.substring(cursorPos);
                    textarea.value = textBefore + placeholder + ' ' + textAfter;
                    textarea.selectionStart = cursorPos + placeholder.length + 1;
                    textarea.selectionEnd = cursorPos + placeholder.length + 1;
                    textarea.focus();
                    inserted = true;
                }
            }

            if (inserted) {
                // Effetto visivo sul pulsante
                $button.addClass('wcos-placeholder-inserted');
                setTimeout(function() {
                    $button.removeClass('wcos-placeholder-inserted wcos-inserting');
                }, 500);
            } else {
                $button.removeClass('wcos-inserting');
            }
        },
        
        confirmDelete: function(e) {
            var confirmMessage = wcos_admin.strings.confirm_delete || 'Sei sicuro di voler eliminare questo cronjob?';
            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return false;
            }
        },
        
        validateField: function() {
            var field = $(this);
            var isValid = true;
            var errorMessage = '';
            
            // Rimuovi messaggi di errore precedenti
            field.removeClass('wcos-field-error');
            field.next('.wcos-field-error-message').remove();
            
            // Validazione specifica per tipo di campo
            switch (field.attr('name')) {
                case 'nome_cronjob':
                    if (field.val().trim() === '') {
                        isValid = false;
                        errorMessage = 'Il nome del cronjob è obbligatorio';
                    }
                    break;
                    
                case 'slug_cronjob':
                    var slug = field.val().trim();
                    if (slug === '') {
                        isValid = false;
                        errorMessage = 'Lo slug del cronjob è obbligatorio';
                    } else if (!/^[a-z0-9-]+$/.test(slug)) {
                        isValid = false;
                        errorMessage = 'Lo slug può contenere solo lettere minuscole, numeri e trattini';
                    }
                    break;
                    
                case 'giorni_da':
                    var giorni_da = parseInt(field.val());
                    if (isNaN(giorni_da) || giorni_da < 0 || giorni_da > 365) {
                        isValid = false;
                        errorMessage = 'Il numero di giorni iniziale deve essere tra 0 e 365';
                    }
                    break;

                case 'giorni_a':
                    var giorni_a = parseInt(field.val());
                    var giorni_da = parseInt($('#giorni_da').val()) || 0;
                    if (isNaN(giorni_a) || giorni_a < 1 || giorni_a > 365) {
                        isValid = false;
                        errorMessage = 'Il numero di giorni finale deve essere tra 1 e 365';
                    } else if (giorni_da >= giorni_a) {
                        isValid = false;
                        errorMessage = 'Il numero di giorni iniziale deve essere minore di quello finale';
                    }
                    break;
                    
                case 'oggetto_email':
                    if (field.val().trim() === '') {
                        isValid = false;
                        errorMessage = 'L\'oggetto email è obbligatorio';
                    }
                    break;
            }
            
            // Mostra errore se necessario
            if (!isValid) {
                field.addClass('wcos-field-error');
                field.after('<div class="wcos-field-error-message">' + errorMessage + '</div>');
            }
            
            return isValid;
        },
        
        previewEmail: function(e) {
            e.preventDefault();
            
            var oggetto = $('#oggetto_email').val();
            var testo = '';
            
            // Ottieni il testo dall'editor
            var editor = tinymce.get('testo_email');
            if (editor && !editor.isHidden()) {
                testo = editor.getContent();
            } else {
                testo = $('#testo_email').val();
            }
            
            if (!oggetto || !testo) {
                alert('Compila oggetto e testo email per vedere l\'anteprima');
                return;
            }
            
            // Apri popup con anteprima
            var previewWindow = window.open('', 'email_preview', 'width=600,height=500,scrollbars=yes');
            previewWindow.document.write(WCOSAdmin.generateEmailPreview(oggetto, testo));
        },
        
        generateEmailPreview: function(oggetto, testo) {
            return `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Anteprima Email</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .email-preview { border: 1px solid #ddd; padding: 20px; }
                        .email-subject { background: #f5f5f5; padding: 10px; margin-bottom: 20px; }
                        .email-content { line-height: 1.6; }
                    </style>
                </head>
                <body>
                    <h1>Anteprima Email</h1>
                    <div class="email-preview">
                        <div class="email-subject">
                            <strong>Oggetto:</strong> ${oggetto}
                        </div>
                        <div class="email-content">
                            ${testo}
                        </div>
                    </div>
                    <p><small>Nota: I placeholder verranno sostituiti con i dati reali dell'ordine quando l'email viene inviata.</small></p>
                </body>
                </html>
            `;
        },
        
        testCronjob: function(e) {
            e.preventDefault();
            
            var cronjobId = $(this).data('cronjob-id');
            if (!cronjobId) {
                alert('ID cronjob non trovato');
                return;
            }
            
            if (!confirm('Vuoi eseguire manualmente questo cronjob per test?')) {
                return;
            }
            
            var button = $(this);
            button.prop('disabled', true).text('Esecuzione in corso...');
            
            $.ajax({
                url: wcos_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'wcos_test_cronjob',
                    cronjob_id: cronjobId,
                    nonce: wcos_admin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        WCOSAdmin.showNotice('Cronjob eseguito con successo: ' + response.data.message, 'success');
                    } else {
                        WCOSAdmin.showNotice('Errore: ' + response.data.message, 'error');
                    }
                },
                error: function() {
                    WCOSAdmin.showNotice('Errore di comunicazione con il server', 'error');
                },
                complete: function() {
                    button.prop('disabled', false).text('Test Cronjob');
                }
            });
        },
        
        initTooltips: function() {
            // Inizializza tooltip per i pulsanti placeholder
            $('.wcos-placeholder-btn').each(function() {
                var title = $(this).attr('title');
                if (title) {
                    $(this).tooltip({
                        position: { my: "center bottom-20", at: "center top" },
                        show: { duration: 200 },
                        hide: { duration: 200 }
                    });
                }
            });
        },
        
        initFormValidation: function() {
            // Validazione form al submit
            $('.wcos-form').on('submit', function(e) {
                var isValid = true;
                var firstErrorField = null;
                
                // Valida tutti i campi obbligatori
                $(this).find('input[required], select[required], textarea[required]').each(function() {
                    if (!WCOSAdmin.validateField.call(this)) {
                        isValid = false;
                        if (!firstErrorField) {
                            firstErrorField = $(this);
                        }
                    }
                });
                
                // Validazione speciale per multiselect
                var statusSelect = $('select[name="status_da_aggiornare[]"]');
                if (statusSelect.length && statusSelect.val() === null) {
                    isValid = false;
                    statusSelect.addClass('wcos-field-error');
                    if (!firstErrorField) {
                        firstErrorField = statusSelect;
                    }
                }
                
                if (!isValid) {
                    e.preventDefault();
                    WCOSAdmin.showNotice('Correggi gli errori nel form prima di continuare', 'error');
                    
                    if (firstErrorField) {
                        firstErrorField.focus();
                    }
                }
            });
        },
        
        showNotice: function(message, type) {
            type = type || 'info';

            var noticeClass = 'notice-' + type;
            var notice = $('<div class="notice ' + noticeClass + ' is-dismissible wcos-notice"><p>' + message + '</p></div>');

            // Rimuovi notice precedenti
            $('.wcos-notice').remove();

            // Aggiungi la nuova notice
            $('.wrap h1').after(notice);

            // Auto-rimuovi dopo 5 secondi
            setTimeout(function() {
                notice.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);

            // Scroll verso l'alto per mostrare la notice
            $('html, body').animate({ scrollTop: 0 }, 300);
        },

        updateRangePreview: function() {
            var giorni_da = parseInt($('#giorni_da').val()) || 0;
            var giorni_a = parseInt($('#giorni_a').val()) || 0;

            if (giorni_da >= 0 && giorni_a > 0) {
                var today = new Date();
                var date_da = new Date(today);
                var date_a = new Date(today);

                date_da.setDate(today.getDate() - giorni_da);
                date_a.setDate(today.getDate() - giorni_a);

                var preview_text = 'Ordini creati dal ' +
                    date_a.toLocaleDateString('it-IT') + ' al ' +
                    date_da.toLocaleDateString('it-IT');

                if (giorni_da >= giorni_a) {
                    preview_text = '<span style="color: #d63638;">⚠ Errore: il valore "Da" deve essere minore del valore "A"</span>';
                }

                $('#range-text').html(preview_text);
                $('#range-preview').show();
            } else {
                $('#range-preview').hide();
            }
        },

        selectAllStatuses: function(e) {
            e.preventDefault();
            $('.wcos-status-checkboxes input[type="checkbox"]').prop('checked', true);
            WCOSAdmin.showNotice('Tutti gli status sono stati selezionati', 'success');
        },

        deselectAllStatuses: function(e) {
            e.preventDefault();
            $('.wcos-status-checkboxes input[type="checkbox"]').prop('checked', false);
            WCOSAdmin.showNotice('Tutti gli status sono stati deselezionati', 'info');
        }
    };
    
    // Inizializza quando il documento è pronto
    $(document).ready(function() {
        WCOSAdmin.init();
    });
    
    // Esponi l'oggetto globalmente per debug
    window.WCOSAdmin = WCOSAdmin;
    
})(jQuery);

// CSS aggiuntivo per gli errori di validazione e placeholder
jQuery(document).ready(function($) {
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .wcos-field-error {
                border-color: #d63638 !important;
                box-shadow: 0 0 2px rgba(214, 54, 56, 0.8);
            }

            .wcos-field-error-message {
                color: #d63638;
                font-size: 12px;
                margin-top: 5px;
                font-style: italic;
            }

            .wcos-placeholder-inserted {
                background-color: #00a32a !important;
                color: white !important;
                border-color: #00a32a !important;
                transform: scale(1.05);
                transition: all 0.3s ease;
            }

            .wcos-inserting {
                opacity: 0.7;
                pointer-events: none;
            }

            .wcos-notice {
                margin: 15px 0;
            }

            /* Miglioramenti per i placeholder buttons */
            .wcos-placeholder-btn {
                position: relative;
                overflow: hidden;
            }

            .wcos-placeholder-btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 0.5s;
            }

            .wcos-placeholder-btn:hover::before {
                left: 100%;
            }
        `)
        .appendTo('head');
});
