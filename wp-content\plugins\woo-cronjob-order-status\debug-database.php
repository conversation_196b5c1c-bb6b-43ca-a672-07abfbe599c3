<?php
/**
 * Debug Database for WooCommerce CronJob Order Status Plugin
 * 
 * This file helps diagnose database issues.
 * Access it via: /wp-content/plugins/woo-cronjob-order-status/debug-database.php
 * 
 * IMPORTANT: Remove this file in production!
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('../../../wp-load.php');
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

// Check if our plugin is active
if (!defined('WCOS_VERSION')) {
    wp_die('WooCommerce CronJob Order Status plugin is not active');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>WCOS Database Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .debug-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .sql-query { background: #e8f4fd; padding: 10px; border-left: 4px solid #0073aa; }
        .fix-button { background: #0073aa; color: white; padding: 10px 20px; border: none; cursor: pointer; margin: 10px 0; }
        .fix-button:hover { background: #005a87; }
    </style>
</head>
<body>
    <h1>WooCommerce CronJob Order Status - Database Debug</h1>
    
    <div class="debug-section">
        <h2>Database Connection Test</h2>
        <?php
        global $wpdb;
        
        if ($wpdb->last_error) {
            echo '<p class="error">✗ Database connection error: ' . esc_html($wpdb->last_error) . '</p>';
        } else {
            echo '<p class="success">✓ Database connection OK</p>';
        }
        
        echo '<p><strong>Database Name:</strong> ' . DB_NAME . '</p>';
        echo '<p><strong>Table Prefix:</strong> ' . $wpdb->prefix . '</p>';
        echo '<p><strong>Expected Table Name:</strong> ' . $wpdb->prefix . WCOS_TABLE_NAME . '</p>';
        ?>
    </div>
    
    <div class="debug-section">
        <h2>Table Existence Check</h2>
        <?php
        $db = new Woo_Cronjob_DB();
        $table_name = $wpdb->prefix . WCOS_TABLE_NAME;
        
        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
        
        if ($table_exists) {
            echo '<p class="success">✓ Table exists: ' . $table_name . '</p>';
            
            // Get table structure
            $columns = $wpdb->get_results("DESCRIBE $table_name");
            echo '<h3>Table Structure:</h3>';
            echo '<table border="1" cellpadding="5">';
            echo '<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>';
            foreach ($columns as $column) {
                echo '<tr>';
                echo '<td>' . esc_html($column->Field) . '</td>';
                echo '<td>' . esc_html($column->Type) . '</td>';
                echo '<td>' . esc_html($column->Null) . '</td>';
                echo '<td>' . esc_html($column->Key) . '</td>';
                echo '<td>' . esc_html($column->Default) . '</td>';
                echo '<td>' . esc_html($column->Extra) . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            
            // Count records
            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
            echo '<p><strong>Records in table:</strong> ' . $count . '</p>';
            
        } else {
            echo '<p class="error">✗ Table does not exist: ' . $table_name . '</p>';
            echo '<form method="post">';
            echo '<input type="hidden" name="action" value="create_table">';
            echo '<button type="submit" class="fix-button">Create Table Now</button>';
            echo '</form>';
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2>Database Permissions Test</h2>
        <?php
        // Test CREATE permission
        $test_table = $wpdb->prefix . 'wcos_test_' . time();
        $create_sql = "CREATE TABLE $test_table (id int(11) NOT NULL AUTO_INCREMENT, test_field varchar(255), PRIMARY KEY (id))";
        
        $create_result = $wpdb->query($create_sql);
        
        if ($create_result !== false) {
            echo '<p class="success">✓ CREATE permission OK</p>';
            
            // Test INSERT permission
            $insert_result = $wpdb->insert($test_table, array('test_field' => 'test_value'), array('%s'));
            
            if ($insert_result !== false) {
                echo '<p class="success">✓ INSERT permission OK</p>';
            } else {
                echo '<p class="error">✗ INSERT permission failed: ' . $wpdb->last_error . '</p>';
            }
            
            // Clean up test table
            $wpdb->query("DROP TABLE $test_table");
            echo '<p class="info">ℹ Test table cleaned up</p>';
            
        } else {
            echo '<p class="error">✗ CREATE permission failed: ' . $wpdb->last_error . '</p>';
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2>Plugin Version & Options</h2>
        <?php
        $plugin_version = get_option('wcos_version');
        echo '<p><strong>Plugin Version in DB:</strong> ' . ($plugin_version ? $plugin_version : 'Not set') . '</p>';
        echo '<p><strong>Current Plugin Version:</strong> ' . WCOS_VERSION . '</p>';
        
        if ($plugin_version !== WCOS_VERSION) {
            echo '<p class="warning">⚠ Version mismatch detected</p>';
            echo '<form method="post">';
            echo '<input type="hidden" name="action" value="update_version">';
            echo '<button type="submit" class="fix-button">Update Version</button>';
            echo '</form>';
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2>Test Data Insertion</h2>
        <?php
        if ($table_exists) {
            echo '<form method="post">';
            echo '<input type="hidden" name="action" value="test_insert">';
            echo '<button type="submit" class="fix-button">Test Insert Sample Data</button>';
            echo '</form>';
        } else {
            echo '<p class="warning">⚠ Cannot test insertion - table does not exist</p>';
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2>Recent Database Errors</h2>
        <?php
        if ($wpdb->last_error) {
            echo '<p class="error">Last Error: ' . esc_html($wpdb->last_error) . '</p>';
        } else {
            echo '<p class="success">No recent database errors</p>';
        }
        
        // Show last query
        if ($wpdb->last_query) {
            echo '<h3>Last Query:</h3>';
            echo '<div class="sql-query">' . esc_html($wpdb->last_query) . '</div>';
        }
        ?>
    </div>
    
    <?php
    // Handle form submissions
    if ($_POST['action'] ?? '') {
        echo '<div class="debug-section">';
        echo '<h2>Action Result</h2>';
        
        switch ($_POST['action']) {
            case 'create_table':
                echo '<p>Creating table...</p>';
                $db->create_tables();
                if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name) {
                    echo '<p class="success">✓ Table created successfully!</p>';
                } else {
                    echo '<p class="error">✗ Failed to create table</p>';
                    if ($wpdb->last_error) {
                        echo '<p class="error">Error: ' . esc_html($wpdb->last_error) . '</p>';
                    }
                }
                break;
                
            case 'update_version':
                update_option('wcos_version', WCOS_VERSION);
                echo '<p class="success">✓ Version updated to ' . WCOS_VERSION . '</p>';
                break;
                
            case 'test_insert':
                $test_data = array(
                    'nome_cronjob' => 'Test CronJob',
                    'slug_cronjob' => 'test-cronjob-' . time(),
                    'cadenza_periodica' => 'daily',
                    'numero_giorni' => 7,
                    'status_da_aggiornare' => array('wc-pending'),
                    'nuovo_status' => 'wc-processing',
                    'oggetto_email' => 'Test Email Subject',
                    'testo_email' => 'Test email content',
                    'attivo' => 1
                );
                
                $result = $db->insert_cronjob($test_data);
                
                if (is_wp_error($result)) {
                    echo '<p class="error">✗ Test insertion failed: ' . $result->get_error_message() . '</p>';
                } else {
                    echo '<p class="success">✓ Test insertion successful! ID: ' . $result . '</p>';
                    
                    // Clean up test data
                    $wpdb->delete($table_name, array('id' => $result), array('%d'));
                    echo '<p class="info">ℹ Test data cleaned up</p>';
                }
                break;
        }
        
        echo '</div>';
        echo '<script>setTimeout(function(){ location.reload(); }, 2000);</script>';
    }
    ?>
    
    <div class="debug-section">
        <h2>Manual SQL Execution</h2>
        <p>If automatic fixes don't work, you can manually execute this SQL:</p>
        <div class="sql-query">
            <?php
            $charset_collate = $wpdb->get_charset_collate();
            $manual_sql = "CREATE TABLE {$table_name} (
    id int(11) NOT NULL AUTO_INCREMENT,
    nome_cronjob varchar(255) NOT NULL,
    slug_cronjob varchar(255) NOT NULL,
    cadenza_periodica varchar(50) NOT NULL,
    numero_giorni int(11) NOT NULL,
    status_da_aggiornare text NOT NULL,
    nuovo_status varchar(50) NOT NULL,
    oggetto_email varchar(500) NOT NULL,
    testo_email text NOT NULL,
    attivo tinyint(1) DEFAULT 1,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY slug_cronjob (slug_cronjob),
    KEY attivo (attivo)
) {$charset_collate};";
            
            echo '<pre>' . esc_html($manual_sql) . '</pre>';
            ?>
        </div>
    </div>
    
    <p><small><strong>Note:</strong> This debug file should be removed in production environments for security reasons.</small></p>
</body>
</html>
