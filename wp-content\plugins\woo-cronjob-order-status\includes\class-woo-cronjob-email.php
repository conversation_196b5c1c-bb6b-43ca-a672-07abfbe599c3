<?php
if (!defined('ABSPATH')) {
    exit;
}

class Woo_Cronjob_Email {
    
    public function __construct() {
        // Hook per personalizzare le email
        add_filter('wcos_email_placeholders', [$this, 'get_default_placeholders'], 10, 3);
    }
    
    /**
     * Invia notifica email al cliente
     */
    public function send_customer_notification($order, $subject, $message, $old_status = '', $new_status = '') {
        if (!$order || !is_a($order, 'WC_Order')) {
            wcos_log('Ordine non valido per invio email', 'error');
            return false;
        }
        
        $customer_email = $order->get_billing_email();
        
        if (empty($customer_email)) {
            wcos_log("Nessun email cliente trovato per ordine #{$order->get_id()}", 'error');
            return false;
        }
        
        // Sostituisci i placeholder nel soggetto e messaggio
        $subject = $this->replace_placeholders($subject, $order, $old_status, $new_status);
        $message = $this->replace_placeholders($message, $order, $old_status, $new_status);
        
        // Prepara l'email HTML
        $html_message = $this->prepare_html_email($message, $order);
        
        // Headers email
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
        );
        
        // Invia l'email
        $sent = wp_mail($customer_email, $subject, $html_message, $headers);
        
        if ($sent) {
            wcos_log("Email inviata con successo a {$customer_email} per ordine #{$order->get_id()}");
            
            // Aggiungi nota all'ordine
            $order->add_order_note(sprintf(
                __('Email di notifica inviata al cliente: %s', 'woo-cronjob-order-status'),
                $subject
            ));
        } else {
            wcos_log("Errore nell'invio email a {$customer_email} per ordine #{$order->get_id()}", 'error');
        }
        
        return $sent;
    }
    
    /**
     * Sostituisce i placeholder nel testo
     */
    private function replace_placeholders($text, $order, $old_status = '', $new_status = '') {
        $placeholders = $this->get_placeholders($order, $old_status, $new_status);
        
        foreach ($placeholders as $placeholder => $value) {
            $text = str_replace($placeholder, $value, $text);
        }
        
        return $text;
    }
    
    /**
     * Ottiene tutti i placeholder disponibili
     */
    private function get_placeholders($order, $old_status = '', $new_status = '') {
        $placeholders = array(
            '{order_number}' => $order->get_order_number(),
            '{order_id}' => $order->get_id(),
            '{order_date}' => $order->get_date_created()->date_i18n(get_option('date_format')),
            '{order_total}' => $order->get_formatted_order_total(),
            '{order_status}' => wc_get_order_status_name($order->get_status()),
            '{customer_first_name}' => $order->get_billing_first_name(),
            '{customer_last_name}' => $order->get_billing_last_name(),
            '{customer_full_name}' => $order->get_billing_first_name() . ' ' . $order->get_billing_last_name(),
            '{customer_email}' => $order->get_billing_email(),
            '{customer_phone}' => $order->get_billing_phone(),
            '{billing_address}' => $this->format_address($order->get_address('billing')),
            '{shipping_address}' => $this->format_address($order->get_address('shipping')),
            '{site_name}' => get_bloginfo('name'),
            '{site_url}' => get_site_url(),
            '{old_status}' => $old_status ? wc_get_order_status_name($old_status) : '',
            '{new_status}' => $new_status ? wc_get_order_status_name($new_status) : '',
            '{order_items}' => $this->get_order_items_html($order),
            '{order_view_url}' => $order->get_view_order_url(),
            '{current_date}' => date_i18n(get_option('date_format')),
            '{current_time}' => date_i18n(get_option('time_format'))
        );
        
        // Permetti ad altri plugin di aggiungere placeholder
        return apply_filters('wcos_email_placeholders', $placeholders, $order, $old_status, $new_status);
    }
    
    /**
     * Formatta un indirizzo
     */
    private function format_address($address) {
        if (empty($address)) {
            return '';
        }
        
        $formatted = '';
        
        if (!empty($address['first_name']) || !empty($address['last_name'])) {
            $formatted .= trim($address['first_name'] . ' ' . $address['last_name']) . "\n";
        }
        
        if (!empty($address['company'])) {
            $formatted .= $address['company'] . "\n";
        }
        
        if (!empty($address['address_1'])) {
            $formatted .= $address['address_1'] . "\n";
        }
        
        if (!empty($address['address_2'])) {
            $formatted .= $address['address_2'] . "\n";
        }
        
        $city_line = '';
        if (!empty($address['postcode'])) {
            $city_line .= $address['postcode'] . ' ';
        }
        if (!empty($address['city'])) {
            $city_line .= $address['city'];
        }
        if (!empty($city_line)) {
            $formatted .= $city_line . "\n";
        }
        
        if (!empty($address['state'])) {
            $formatted .= $address['state'] . "\n";
        }
        
        if (!empty($address['country'])) {
            $countries = WC()->countries->get_countries();
            $formatted .= isset($countries[$address['country']]) ? $countries[$address['country']] : $address['country'];
        }
        
        return $formatted;
    }
    
    /**
     * Ottiene gli articoli dell'ordine in formato HTML
     */
    private function get_order_items_html($order) {
        $items_html = '<table style="width: 100%; border-collapse: collapse;">';
        $items_html .= '<thead><tr>';
        $items_html .= '<th style="border: 1px solid #ddd; padding: 8px; text-align: left;">' . __('Prodotto', 'woo-cronjob-order-status') . '</th>';
        $items_html .= '<th style="border: 1px solid #ddd; padding: 8px; text-align: center;">' . __('Quantità', 'woo-cronjob-order-status') . '</th>';
        $items_html .= '<th style="border: 1px solid #ddd; padding: 8px; text-align: right;">' . __('Prezzo', 'woo-cronjob-order-status') . '</th>';
        $items_html .= '</tr></thead><tbody>';
        
        foreach ($order->get_items() as $item) {
            $product = $item->get_product();
            $product_name = $item->get_name();
            
            if ($product && $product->get_sku()) {
                $product_name .= ' (SKU: ' . $product->get_sku() . ')';
            }
            
            $items_html .= '<tr>';
            $items_html .= '<td style="border: 1px solid #ddd; padding: 8px;">' . esc_html($product_name) . '</td>';
            $items_html .= '<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">' . $item->get_quantity() . '</td>';
            $items_html .= '<td style="border: 1px solid #ddd; padding: 8px; text-align: right;">' . $order->get_formatted_line_subtotal($item) . '</td>';
            $items_html .= '</tr>';
        }
        
        $items_html .= '</tbody></table>';
        
        return $items_html;
    }
    
    /**
     * Prepara l'email in formato HTML
     */
    private function prepare_html_email($message, $order) {
        // Template HTML base
        $html = '<!DOCTYPE html>';
        $html .= '<html><head>';
        $html .= '<meta charset="UTF-8">';
        $html .= '<meta name="viewport" content="width=device-width, initial-scale=1.0">';
        $html .= '<title>' . get_bloginfo('name') . '</title>';
        $html .= '<style>';
        $html .= 'body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }';
        $html .= '.email-container { max-width: 600px; margin: 0 auto; padding: 20px; }';
        $html .= '.email-header { background-color: #f8f9fa; padding: 20px; text-align: center; border-bottom: 2px solid #007cba; }';
        $html .= '.email-content { padding: 20px; }';
        $html .= '.email-footer { background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666; }';
        $html .= 'table { border-collapse: collapse; width: 100%; }';
        $html .= 'th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }';
        $html .= 'th { background-color: #f2f2f2; }';
        $html .= '</style>';
        $html .= '</head><body>';
        
        $html .= '<div class="email-container">';
        $html .= '<div class="email-header">';
        $html .= '<h1>' . get_bloginfo('name') . '</h1>';
        $html .= '</div>';
        
        $html .= '<div class="email-content">';
        $html .= wpautop($message); // Converte i line break in paragrafi
        $html .= '</div>';
        
        $html .= '<div class="email-footer">';
        $html .= '<p>' . sprintf(__('Questa email è stata inviata da %s', 'woo-cronjob-order-status'), get_bloginfo('name')) . '</p>';
        $html .= '<p>' . sprintf(__('Se hai domande, contattaci all\'indirizzo %s', 'woo-cronjob-order-status'), get_option('admin_email')) . '</p>';
        $html .= '</div>';
        
        $html .= '</div>';
        $html .= '</body></html>';
        
        return $html;
    }
    
    /**
     * Ottiene i placeholder di default
     */
    public function get_default_placeholders($placeholders, $order, $old_status, $new_status) {
        return $placeholders;
    }
    
    /**
     * Invia email di test
     */
    public function send_test_email($email, $subject, $message) {
        // Crea un ordine fittizio per i placeholder
        $test_order = $this->create_test_order();
        
        if (!$test_order) {
            return false;
        }
        
        return $this->send_customer_notification($test_order, $subject, $message, 'pending', 'processing');
    }
    
    /**
     * Crea un ordine fittizio per test
     */
    private function create_test_order() {
        // Questo è solo per i placeholder, non viene salvato
        $order = new WC_Order();
        $order->set_id(12345);
        $order->set_order_number('12345');
        $order->set_billing_first_name('Mario');
        $order->set_billing_last_name('Rossi');
        $order->set_billing_email('<EMAIL>');
        $order->set_total(100.00);
        $order->set_status('processing');
        
        return $order;
    }
    
    /**
     * Ottiene la lista dei placeholder disponibili per l'interfaccia admin
     */
    public static function get_available_placeholders() {
        return array(
            '{order_number}' => __('Numero ordine', 'woo-cronjob-order-status'),
            '{order_id}' => __('ID ordine', 'woo-cronjob-order-status'),
            '{order_date}' => __('Data ordine', 'woo-cronjob-order-status'),
            '{order_total}' => __('Totale ordine', 'woo-cronjob-order-status'),
            '{order_status}' => __('Stato ordine attuale', 'woo-cronjob-order-status'),
            '{customer_first_name}' => __('Nome cliente', 'woo-cronjob-order-status'),
            '{customer_last_name}' => __('Cognome cliente', 'woo-cronjob-order-status'),
            '{customer_full_name}' => __('Nome completo cliente', 'woo-cronjob-order-status'),
            '{customer_email}' => __('Email cliente', 'woo-cronjob-order-status'),
            '{billing_address}' => __('Indirizzo di fatturazione', 'woo-cronjob-order-status'),
            '{shipping_address}' => __('Indirizzo di spedizione', 'woo-cronjob-order-status'),
            '{site_name}' => __('Nome sito', 'woo-cronjob-order-status'),
            '{old_status}' => __('Stato precedente', 'woo-cronjob-order-status'),
            '{new_status}' => __('Nuovo stato', 'woo-cronjob-order-status'),
            '{order_items}' => __('Articoli ordine (tabella)', 'woo-cronjob-order-status'),
            '{order_view_url}' => __('URL visualizzazione ordine', 'woo-cronjob-order-status'),
            '{current_date}' => __('Data corrente', 'woo-cronjob-order-status'),
            '{current_time}' => __('Ora corrente', 'woo-cronjob-order-status')
        );
    }
}
