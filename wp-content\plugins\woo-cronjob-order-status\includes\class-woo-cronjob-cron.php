<?php
if (!defined('ABSPATH')) {
    exit;
}

class Woo_Cronjob_Cron {
    private $db;
    private $email;
    
    public function __construct() {
        $this->db = new Woo_Cronjob_DB();
        $this->email = new Woo_Cronjob_Email();
        
        // Registra il cron principale
        add_action('wcos_check_cronjobs', [$this, 'check_and_execute_cronjobs']);
        
        // Registra i cron individuali dinamicamente
        add_action('init', [$this, 'register_individual_cronjobs']);
    }
    
    /**
     * Registra i cron job individuali
     */
    public function register_individual_cronjobs() {
        $cronjobs = $this->db->get_all_cronjobs(true); // Solo quelli attivi
        
        foreach ($cronjobs as $cronjob) {
            $hook = 'wcos_execute_cronjob_' . $cronjob->slug_cronjob;
            add_action($hook, [$this, 'execute_single_cronjob'], 10, 1);
        }
    }
    
    /**
     * Controlla e esegue tutti i cronjob attivi
     */
    public function check_and_execute_cronjobs() {
        wcos_log('Controllo cronjob programmati iniziato');
        
        $cronjobs = $this->db->get_all_cronjobs(true);
        
        foreach ($cronjobs as $cronjob) {
            $this->execute_cronjob($cronjob);
        }
        
        wcos_log('Controllo cronjob programmati completato');
    }
    
    /**
     * Esegue un singolo cronjob tramite hook
     */
    public function execute_single_cronjob($slug = null) {
        if (!$slug) {
            // Estrai lo slug dal nome dell'azione corrente
            $current_filter = current_filter();
            $slug = str_replace('wcos_execute_cronjob_', '', $current_filter);
        }
        
        $cronjob = $this->db->get_cronjob_by_slug($slug);
        
        if ($cronjob && $cronjob->attivo) {
            $this->execute_cronjob($cronjob);
        }
    }
    
    /**
     * Esegue un cronjob specifico
     */
    private function execute_cronjob($cronjob) {
        wcos_log("Esecuzione cronjob: {$cronjob->nome_cronjob} (ID: {$cronjob->id})");
        
        try {
            // Calcola il range di date
            $giorni_da = intval($cronjob->giorni_da);
            $giorni_a = intval($cronjob->giorni_a);

            $date_from = date('Y-m-d H:i:s', strtotime("-{$giorni_a} days"));
            $date_to = date('Y-m-d H:i:s', strtotime("-{$giorni_da} days"));

            // Ottieni gli stati da aggiornare
            $statuses_to_update = explode(',', $cronjob->status_da_aggiornare);
            $statuses_to_update = array_map('trim', $statuses_to_update);

            // Trova gli ordini che corrispondono ai criteri
            $orders = $this->find_matching_orders($statuses_to_update, $date_from, $date_to);
            
            if (empty($orders)) {
                wcos_log("Nessun ordine trovato per il cronjob: {$cronjob->nome_cronjob}");
                return;
            }
            
            $updated_count = 0;
            $email_count = 0;
            
            foreach ($orders as $order_id) {
                try {
                    $order = wc_get_order($order_id);

                    if (!$order) {
                        wcos_log("Ordine #{$order_id} non trovato, saltato", 'warning');
                        continue;
                    }

                    // Aggiorna lo stato dell'ordine
                    $old_status = $order->get_status();
                    $new_status = str_replace('wc-', '', $cronjob->nuovo_status);

                    // Verifica che il nuovo status sia valido
                    $valid_statuses = array_keys(wc_get_order_statuses());
                    $full_new_status = 'wc-' . $new_status;

                    if (!in_array($full_new_status, $valid_statuses)) {
                        wcos_log("Status '{$full_new_status}' non valido per ordine #{$order_id}", 'error');
                        continue;
                    }

                    $order->update_status($new_status, sprintf(
                        __('Stato aggiornato automaticamente da cronjob: %s', 'woo-cronjob-order-status'),
                        $cronjob->nome_cronjob
                    ));

                    $updated_count++;

                    // Invia email al cliente se configurata
                    if (!empty($cronjob->oggetto_email) && !empty($cronjob->testo_email)) {
                        $email_sent = $this->email->send_customer_notification(
                            $order,
                            $cronjob->oggetto_email,
                            $cronjob->testo_email,
                            $old_status,
                            $new_status
                        );

                        if ($email_sent) {
                            $email_count++;
                        }
                    }

                    wcos_log("Ordine #{$order_id} aggiornato da '{$old_status}' a '{$new_status}'");

                } catch (Exception $e) {
                    wcos_log("Errore durante l'elaborazione dell'ordine #{$order_id}: " . $e->getMessage(), 'error');
                    continue;
                }
            }
            
            wcos_log("Cronjob '{$cronjob->nome_cronjob}' completato: {$updated_count} ordini aggiornati, {$email_count} email inviate");
            
        } catch (Exception $e) {
            wcos_log("Errore durante l'esecuzione del cronjob '{$cronjob->nome_cronjob}': " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * Trova gli ordini che corrispondono ai criteri
     */
    private function find_matching_orders($statuses, $date_from, $date_to) {
        $args = array(
            'status' => $statuses,
            'date_created' => $date_from . '...' . $date_to,
            'limit' => -1,
            'return' => 'ids'
        );

        return wc_get_orders($args);
    }
    
    /**
     * Programma un cronjob
     */
    public static function schedule_cronjob($slug, $frequency) {
        $hook = 'wcos_execute_cronjob_' . $slug;
        
        // Rimuovi il cron esistente
        wp_clear_scheduled_hook($hook);
        
        // Programma il nuovo cron
        wp_schedule_event(time(), $frequency, $hook);
        
        wcos_log("Cronjob programmato: {$slug} con frequenza {$frequency}");
    }
    
    /**
     * Rimuove la programmazione di un cronjob
     */
    public static function unschedule_cronjob($slug) {
        $hook = 'wcos_execute_cronjob_' . $slug;
        wp_clear_scheduled_hook($hook);
        
        wcos_log("Cronjob rimosso dalla programmazione: {$slug}");
    }
    
    /**
     * Ottiene il prossimo orario di esecuzione di un cronjob
     */
    public static function get_next_scheduled($slug) {
        $hook = 'wcos_execute_cronjob_' . $slug;
        return wp_next_scheduled($hook);
    }
    
    /**
     * Verifica se un cronjob è programmato
     */
    public static function is_scheduled($slug) {
        $hook = 'wcos_execute_cronjob_' . $slug;
        return wp_next_scheduled($hook) !== false;
    }
    
    /**
     * Esegue manualmente un cronjob (per test)
     */
    public function manual_execute($cronjob_id) {
        $cronjob = $this->db->get_cronjob($cronjob_id);

        if (!$cronjob) {
            return new WP_Error('not_found', __('Cronjob non trovato', 'woo-cronjob-order-status'));
        }

        // Log dell'esecuzione manuale
        wcos_log("Esecuzione manuale del cronjob: {$cronjob->nome_cronjob} (ID: {$cronjob->id})");

        try {
            $this->execute_cronjob($cronjob);
            wcos_log("Esecuzione manuale completata con successo per cronjob: {$cronjob->nome_cronjob}");
            return true;
        } catch (Exception $e) {
            wcos_log("Errore durante l'esecuzione manuale del cronjob '{$cronjob->nome_cronjob}': " . $e->getMessage(), 'error');
            return new WP_Error('execution_failed', $e->getMessage());
        }
    }
    
    /**
     * Ottiene statistiche sui cronjob
     */
    public function get_cronjob_stats() {
        $cronjobs = $this->db->get_all_cronjobs();
        $stats = array(
            'total' => count($cronjobs),
            'active' => 0,
            'scheduled' => 0
        );
        
        foreach ($cronjobs as $cronjob) {
            if ($cronjob->attivo) {
                $stats['active']++;
                
                if (self::is_scheduled($cronjob->slug_cronjob)) {
                    $stats['scheduled']++;
                }
            }
        }
        
        return $stats;
    }
    
    /**
     * Pulisce i cron job orfani (non più presenti nel database)
     */
    public function cleanup_orphaned_crons() {
        $cronjobs = $this->db->get_all_cronjobs();
        $active_slugs = array();
        
        foreach ($cronjobs as $cronjob) {
            $active_slugs[] = $cronjob->slug_cronjob;
        }
        
        // Ottieni tutti i cron job programmati
        $crons = _get_cron_array();
        $cleaned = 0;
        
        foreach ($crons as $timestamp => $cron) {
            foreach ($cron as $hook => $events) {
                if (strpos($hook, 'wcos_execute_cronjob_') === 0) {
                    $slug = str_replace('wcos_execute_cronjob_', '', $hook);
                    
                    if (!in_array($slug, $active_slugs)) {
                        wp_clear_scheduled_hook($hook);
                        $cleaned++;
                        wcos_log("Rimosso cron orfano: {$hook}");
                    }
                }
            }
        }
        
        return $cleaned;
    }
}
