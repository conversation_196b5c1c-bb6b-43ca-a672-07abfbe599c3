<?php
/**
 * Test Placeholders for WooCommerce CronJob Order Status Plugin
 * 
 * This file helps test the placeholder functionality.
 * Access it via: /wp-content/plugins/woo-cronjob-order-status/test-placeholders.php
 * 
 * IMPORTANT: Remove this file in production!
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('../../../wp-load.php');
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

// Check if our plugin is active
if (!defined('WCOS_VERSION')) {
    wp_die('WooCommerce CronJob Order Status plugin is not active');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>WCOS Placeholder Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        
        /* Test placeholder styles */
        .wcos-placeholders {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 12px;
        }

        .wcos-placeholder-btn {
            background: #f6f7f7;
            border: 1px solid #c3c4c7;
            border-radius: 4px;
            padding: 6px 10px;
            font-size: 12px;
            font-family: 'Courier New', Courier, monospace;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #1d2327;
            font-weight: 500;
            line-height: 1.2;
            min-height: 28px;
            display: inline-flex;
            align-items: center;
            text-decoration: none;
        }

        .wcos-placeholder-btn:hover {
            background: #0073aa;
            color: #ffffff;
            border-color: #0073aa;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .wcos-placeholder-btn:active {
            transform: translateY(0);
        }

        .wcos-placeholder-btn:focus {
            outline: 2px solid #0073aa;
            outline-offset: 2px;
        }
        
        .test-textarea {
            width: 100%;
            height: 200px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>WooCommerce CronJob Order Status - Placeholder Test</h1>
    
    <div class="test-section">
        <h2>Placeholder Visual Test</h2>
        <p>Questi sono i pulsanti placeholder come dovrebbero apparire:</p>
        
        <div class="wcos-placeholders">
            <?php 
            $placeholders = Woo_Cronjob_Email::get_available_placeholders();
            foreach ($placeholders as $placeholder => $description): 
            ?>
                <button type="button" class="wcos-placeholder-btn test-placeholder" 
                        data-placeholder="<?php echo esc_attr($placeholder); ?>" 
                        title="<?php echo esc_attr($description); ?>">
                    <?php echo esc_html($placeholder); ?>
                </button>
            <?php endforeach; ?>
        </div>
        
        <p class="info">I pulsanti dovrebbero essere visibili con testo nero su sfondo grigio chiaro, e diventare blu al passaggio del mouse.</p>
    </div>
    
    <div class="test-section">
        <h2>Placeholder Insertion Test</h2>
        <p>Clicca sui pulsanti sopra per inserire i placeholder in questa textarea:</p>
        
        <textarea id="test_textarea" class="test-textarea" placeholder="I placeholder verranno inseriti qui..."></textarea>
        
        <p><button type="button" id="clear_textarea">Pulisci Textarea</button></p>
        
        <div id="insertion_log" style="background: #f0f0f0; padding: 10px; margin-top: 10px; min-height: 50px;">
            <strong>Log inserimenti:</strong><br>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Sample Email Preview</h2>
        <p>Esempio di come i placeholder vengono sostituiti:</p>
        
        <?php
        // Create a sample order for testing
        $sample_data = array(
            '{order_number}' => '12345',
            '{customer_first_name}' => 'Mario',
            '{customer_last_name}' => 'Rossi',
            '{order_total}' => '€99,99',
            '{order_date}' => date('d/m/Y'),
            '{site_name}' => get_bloginfo('name'),
            '{old_status}' => 'In attesa di pagamento',
            '{new_status}' => 'In elaborazione'
        );
        
        $sample_template = "Ciao {customer_first_name},\n\nIl tuo ordine #{order_number} del {order_date} è stato aggiornato.\n\nDettagli:\n- Totale: {order_total}\n- Stato precedente: {old_status}\n- Nuovo stato: {new_status}\n\nGrazie per aver scelto {site_name}!";
        
        $processed_template = $sample_template;
        foreach ($sample_data as $placeholder => $value) {
            $processed_template = str_replace($placeholder, $value, $processed_template);
        }
        ?>
        
        <h3>Template con placeholder:</h3>
        <pre style="background: #f9f9f9; padding: 10px; border-left: 4px solid #ccc;"><?php echo esc_html($sample_template); ?></pre>
        
        <h3>Risultato finale:</h3>
        <pre style="background: #e8f5e8; padding: 10px; border-left: 4px solid #4caf50;"><?php echo esc_html($processed_template); ?></pre>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
    jQuery(document).ready(function($) {
        var insertionCount = 0;
        
        // Handle placeholder button clicks
        $('.test-placeholder').on('click', function(e) {
            e.preventDefault();
            
            var placeholder = $(this).data('placeholder');
            var textarea = $('#test_textarea')[0];
            
            if (textarea) {
                var cursorPos = textarea.selectionStart || 0;
                var textBefore = textarea.value.substring(0, cursorPos);
                var textAfter = textarea.value.substring(cursorPos);
                textarea.value = textBefore + placeholder + ' ' + textAfter;
                textarea.selectionStart = cursorPos + placeholder.length + 1;
                textarea.selectionEnd = cursorPos + placeholder.length + 1;
                textarea.focus();
                
                // Log the insertion
                insertionCount++;
                $('#insertion_log').append('<div>' + insertionCount + '. Inserito: <code>' + placeholder + '</code> alla posizione ' + cursorPos + '</div>');
                
                // Visual feedback
                $(this).css('background-color', '#00a32a').css('color', 'white');
                setTimeout(function() {
                    $('.test-placeholder').css('background-color', '').css('color', '');
                }, 300);
            }
        });
        
        // Clear textarea
        $('#clear_textarea').on('click', function() {
            $('#test_textarea').val('');
            $('#insertion_log').html('<strong>Log inserimenti:</strong><br>');
            insertionCount = 0;
        });
        
        // Test hover effects
        $('.test-placeholder').hover(
            function() {
                console.log('Hover su:', $(this).data('placeholder'));
            },
            function() {
                console.log('Hover out da:', $(this).data('placeholder'));
            }
        );
    });
    </script>
    
    <p><small><strong>Note:</strong> This test file should be removed in production environments for security reasons.</small></p>
</body>
</html>
